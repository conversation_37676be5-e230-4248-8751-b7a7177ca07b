from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary, 
    Storage, StorageItem, IncomingOperation, IncomingOperationItem, 
    IncomingOperationFile, OutgoingOperation, OutgoingOperationItem,
    OutgoingOperationFile, OperationModification, IncomingReturn,
    IncomingReturnItem, IncomingReturnFile, OutgoingReturn,
    OutgoingReturnItem, OutgoingReturnFile, DamageOperation,
    DamageOperationItem, DamageOperationFile, StorageTransfer,
    StorageTransferItem, StorageTransferFile
)

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'full_name', 'user_type', 'is_active')
    list_filter = ('user_type', 'is_active')
    search_fields = ('username', 'full_name')
    
    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {'fields': ('full_name', 'user_type')}),
    )

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at')
    search_fields = ('name',)
    list_per_page = 20

@admin.register(Station)
class StationAdmin(admin.ModelAdmin):
    list_display = ('name', 'address', 'created_at')
    search_fields = ('name', 'address')
    list_per_page = 20

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Beneficiary)
class BeneficiaryAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

@admin.register(Storage)
class StorageAdmin(admin.ModelAdmin):
    list_display = ('name', 'classification', 'keeper_name', 'phone_number', 'created_at')
    list_filter = ('classification',)
    search_fields = ('name', 'keeper_name')
    list_per_page = 20

@admin.register(StorageItem)
class StorageItemAdmin(admin.ModelAdmin):
    list_display = ('storage', 'category', 'unit_of_measure', 'opening_balance', 'current_quantity', 'updated_at')
    list_filter = ('storage', 'category', 'unit_of_measure')
    search_fields = ('storage__name', 'category__name')
    list_per_page = 20

class IncomingOperationItemInline(admin.TabularInline):
    model = IncomingOperationItem
    extra = 1

class IncomingOperationFileInline(admin.TabularInline):
    model = IncomingOperationFile
    extra = 1

@admin.register(IncomingOperation)
class IncomingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'supplier', 'operation_date', 'created_by', 'created_at')
    list_filter = ('storage', 'supplier', 'station', 'operation_date')
    search_fields = ('paper_number', 'supply_document_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'operation_date'
    inlines = [IncomingOperationItemInline, IncomingOperationFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# إدارة عمليات الصادر
class OutgoingOperationItemInline(admin.TabularInline):
    model = OutgoingOperationItem
    extra = 1

class OutgoingOperationFileInline(admin.TabularInline):
    model = OutgoingOperationFile
    extra = 1

@admin.register(OutgoingOperation)
class OutgoingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'beneficiary', 'operation_date', 'created_by', 'created_at')
    list_filter = ('storage', 'beneficiary', 'operation_date')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'operation_date'
    inlines = [OutgoingOperationItemInline, OutgoingOperationFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(OutgoingOperationItem)
class OutgoingOperationItemAdmin(admin.ModelAdmin):
    list_display = ('outgoing_operation', 'category', 'exported_quantity', 'transfer_date', 'actual_transfer_date')
    list_filter = ('category', 'outgoing_operation__storage', 'transfer_date')
    search_fields = ('outgoing_operation__paper_number', 'category__name')
    list_per_page = 20

@admin.register(OutgoingOperationFile)
class OutgoingOperationFileAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'outgoing_operation', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('file_name', 'outgoing_operation__paper_number')
    list_per_page = 20

@admin.register(OperationModification)
class OperationModificationAdmin(admin.ModelAdmin):
    list_display = ('storage', 'operation_type', 'category', 'previous_quantity', 'new_quantity', 'modified_by', 'modification_date')
    list_filter = ('operation_type', 'storage', 'category', 'modification_date')
    search_fields = ('storage__name', 'category__name', 'notes')
    date_hierarchy = 'modification_date'
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.modified_by = request.user
        super().save_model(request, obj, form, change)

# إدارة مرتجعات الوارد
class IncomingReturnItemInline(admin.TabularInline):
    model = IncomingReturnItem
    extra = 1

class IncomingReturnFileInline(admin.TabularInline):
    model = IncomingReturnFile
    extra = 1

@admin.register(IncomingReturn)
class IncomingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'incoming_operation', 'return_date', 'storage', 'supplier', 'created_by', 'created_at')
    list_filter = ('return_date', 'incoming_operation__storage', 'incoming_operation__supplier')
    search_fields = ('paper_number', 'incoming_operation__paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'return_date'
    inlines = [IncomingReturnItemInline, IncomingReturnFileInline]
    list_per_page = 20
    
    def storage(self, obj):
        return obj.storage.name
    storage.short_description = 'المخزن'
    
    def supplier(self, obj):
        return obj.supplier.full_name
    supplier.short_description = 'المورد'
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(IncomingReturnItem)
class IncomingReturnItemAdmin(admin.ModelAdmin):
    list_display = ('incoming_return', 'category', 'returned_quantity', 'expected_return_date', 'actual_return_date')
    list_filter = ('category', 'expected_return_date', 'actual_return_date')
    search_fields = ('incoming_return__paper_number', 'category__name')
    list_per_page = 20

# إدارة مرتجعات الصادر
class OutgoingReturnItemInline(admin.TabularInline):
    model = OutgoingReturnItem
    extra = 1

class OutgoingReturnFileInline(admin.TabularInline):
    model = OutgoingReturnFile
    extra = 1

@admin.register(OutgoingReturn)
class OutgoingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'outgoing_operation', 'return_date', 'storage', 'beneficiary', 'created_by', 'created_at')
    list_filter = ('return_date', 'outgoing_operation__storage', 'outgoing_operation__beneficiary')
    search_fields = ('paper_number', 'outgoing_operation__paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'return_date'
    inlines = [OutgoingReturnItemInline, OutgoingReturnFileInline]
    list_per_page = 20
    
    def storage(self, obj):
        return obj.storage.name
    storage.short_description = 'المخزن'
    
    def beneficiary(self, obj):
        return obj.beneficiary.full_name
    beneficiary.short_description = 'المستفيد'
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(OutgoingReturnItem)
class OutgoingReturnItemAdmin(admin.ModelAdmin):
    list_display = ('outgoing_return', 'category', 'returned_quantity', 'expected_return_date', 'actual_return_date')
    list_filter = ('category', 'expected_return_date', 'actual_return_date')
    search_fields = ('outgoing_return__paper_number', 'category__name')
    list_per_page = 20

# إدارة عمليات التلف
class DamageOperationItemInline(admin.TabularInline):
    model = DamageOperationItem
    extra = 1

class DamageOperationFileInline(admin.TabularInline):
    model = DamageOperationFile
    extra = 1

@admin.register(DamageOperation)
class DamageOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'damage_date', 'deliverer_name', 'receiver_name', 'created_by', 'created_at')
    list_filter = ('storage', 'damage_date', 'created_at')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name', 'statement')
    date_hierarchy = 'damage_date'
    inlines = [DamageOperationItemInline, DamageOperationFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(DamageOperationItem)
class DamageOperationItemAdmin(admin.ModelAdmin):
    list_display = ('damage_operation', 'category', 'damaged_quantity', 'reason', 'notes')
    list_filter = ('category', 'reason', 'damage_operation__storage')
    search_fields = ('damage_operation__paper_number', 'category__name', 'notes')
    list_per_page = 20

# إدارة عمليات التحويل المخزني
class StorageTransferItemInline(admin.TabularInline):
    model = StorageTransferItem
    extra = 1

class StorageTransferFileInline(admin.TabularInline):
    model = StorageTransferFile
    extra = 1

@admin.register(StorageTransfer)
class StorageTransferAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'from_storage', 'to_storage', 'transfer_date', 'deliverer_name', 'receiver_name', 'created_by', 'created_at')
    list_filter = ('from_storage', 'to_storage', 'transfer_date', 'created_at')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name', 'statement')
    date_hierarchy = 'transfer_date'
    inlines = [StorageTransferItemInline, StorageTransferFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(StorageTransferItem)
class StorageTransferItemAdmin(admin.ModelAdmin):
    list_display = ('storage_transfer', 'category', 'transferred_quantity', 'reason', 'notes')
    list_filter = ('category', 'reason', 'storage_transfer__from_storage', 'storage_transfer__to_storage')
    search_fields = ('storage_transfer__paper_number', 'category__name', 'notes')
    list_per_page = 20
