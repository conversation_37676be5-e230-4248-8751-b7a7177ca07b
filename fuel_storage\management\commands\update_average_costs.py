from django.core.management.base import BaseCommand
from django.db.models import Avg
from fuel_storage.models import StorageItem, IncomingOperationItem

class Command(BaseCommand):
    help = 'تحديث متوسط التكلفة لجميع أصناف المخازن'

    def handle(self, *args, **options):
        self.stdout.write('بدء تحديث متوسط التكلفة...')
        
        updated_count = 0
        
        for item in StorageItem.objects.all():
            # حساب متوسط سعر الشراء من عمليات الوارد
            avg_price = IncomingOperationItem.objects.filter(
                category=item.category,
                incoming_operation__storage=item.storage
            ).aggregate(avg_price=Avg('unit_price'))['avg_price']
            
            if avg_price:
                item.average_cost = avg_price
                item.save()
                updated_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'تم تحديث متوسط التكلفة لـ {updated_count} صنف')
        )
