{% extends 'base.html' %}

{% block title %}إدارة المستخدمين - نظام إدارة مخازن المحروقات{% endblock %}

{% block extra_css %}
<style>
    .admin-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        border-radius: 0 0 20px 20px;
    }

    .filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        margin-bottom: 2rem;
    }

    .users-table {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .table th {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid #f1f3f4;
    }

    .btn-action {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin: 0 2px;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin-left: 10px;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        color: white;
    }

    .user-type-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .type-manager {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
    }

    .type-operator {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
    }

    .type-viewer {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="admin-header text-center">
    <div class="container">
        <h1 style="margin: 0; font-weight: 700; display: flex; align-items: center; justify-content: center; gap: 1rem;">
            <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-users"></i>
            </div>
            إدارة المستخدمين
        </h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">إضافة وتعديل وحذف المستخدمين في النظام</p>
    </div>
</div>

<!-- أزرار التحكم -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{% url 'fuel_storage:dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة للوحة التحكم
        </a>
    </div>
    <div>
        <a href="{% url 'fuel_storage:create_user' %}" class="btn btn-primary" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); border: none; border-radius: 25px; padding: 0.75rem 2rem;">
            <i class="fas fa-plus"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="filter-card">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني">
            </div>
            <div class="col-md-3">
                <label for="user_type" class="form-label">نوع المستخدم</label>
                <select class="form-select" id="user_type" name="user_type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in user_types %}
                        <option value="{{ value }}" {% if user_type_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="users-table">
    <div class="card-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 1.5rem;">
        <h5 style="margin: 0; font-weight: 700; display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-list"></i>
            قائمة المستخدمين
            <span class="badge bg-light text-dark ms-2">{{ page_obj.paginator.count }} مستخدم</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>نوع المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user_obj in page_obj %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar">
                                            {{ user_obj.full_name|first|upper }}
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">{{ user_obj.full_name }}</div>
                                            <div style="font-size: 0.8rem; color: #6c757d;">{{ user_obj.username }}</div>
                                            {% if user_obj.is_superuser %}
                                                <span class="badge bg-danger">مدير عام</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="user-type-badge type-{{ user_obj.user_type }}">
                                        {{ user_obj.get_user_type_display }}
                                    </span>
                                </td>
                                <td>{{ user_obj.email|default:"-" }}</td>
                                <td>{{ user_obj.date_joined|date:"Y-m-d" }}</td>
                                <td>
                                    {% if user_obj.is_active %}
                                        <span class="status-badge status-active">نشط</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{% url 'fuel_storage:edit_user' user_obj.id %}" 
                                           class="btn btn-outline-primary btn-action" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if user_obj != user %}
                                            <button type="button" class="btn btn-outline-warning btn-action" 
                                                    onclick="toggleStatus('{% url 'fuel_storage:toggle_user_status' user_obj.id %}')"
                                                    title="{% if user_obj.is_active %}إلغاء التفعيل{% else %}تفعيل{% endif %}">
                                                <i class="fas fa-{% if user_obj.is_active %}ban{% else %}check{% endif %}"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger btn-action" 
                                                    onclick="confirmDelete('{% url 'fuel_storage:delete_user' user_obj.id %}', '{{ user_obj.full_name }}')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if page_obj.has_other_pages %}
                <div class="p-3">
                    <nav aria-label="تصفح المستخدمين">
                        <ul class="pagination justify-content-center mb-0">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مستخدمين</h5>
                <p class="text-muted">لم يتم العثور على أي مستخدمين مطابقين لمعايير البحث.</p>
                <a href="{% url 'fuel_storage:create_user' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- CSRF Token للعمليات AJAX -->
{% csrf_token %}

<script>
// تأكيد الحذف
function confirmDelete(url, itemName) {
    if (confirm(`هل أنت متأكد من حذف "${itemName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'حدث خطأ أثناء الحذف');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الحذف');
        });
    }
}

// تبديل الحالة
function toggleStatus(url) {
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ أثناء تغيير الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تغيير الحالة');
    });
}
</script>
{% endblock %}
