# Generated by Django 4.2.7 on 2025-08-06 01:15

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('fuel_storage', '0002_account_accountingperiod_accounttype_budget_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='accountingperiod',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='budget',
            name='created_by',
        ),
        migrations.AlterUniqueTogether(
            name='budgetitem',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='budgetitem',
            name='account',
        ),
        migrations.RemoveField(
            model_name='budgetitem',
            name='budget',
        ),
        migrations.RemoveField(
            model_name='cashflow',
            name='account',
        ),
        migrations.RemoveField(
            model_name='cashflow',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='cashflow',
            name='journal_entry',
        ),
        migrations.RemoveField(
            model_name='cashflow',
            name='payment',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='beneficiary',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='incoming_operation',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='incoming_return',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='outgoing_operation',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='outgoing_return',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='supplier',
        ),
        migrations.RemoveField(
            model_name='invoiceitem',
            name='category',
        ),
        migrations.RemoveField(
            model_name='invoiceitem',
            name='invoice',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='accounting_period',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='incoming_operation',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='incoming_return',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='outgoing_operation',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='outgoing_return',
        ),
        migrations.RemoveField(
            model_name='journalentry',
            name='posted_by',
        ),
        migrations.RemoveField(
            model_name='payment',
            name='beneficiary',
        ),
        migrations.RemoveField(
            model_name='payment',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='payment',
            name='invoice',
        ),
        migrations.RemoveField(
            model_name='payment',
            name='payment_method',
        ),
        migrations.RemoveField(
            model_name='payment',
            name='supplier',
        ),
        migrations.RemoveField(
            model_name='paymentmethod',
            name='account',
        ),
        migrations.RemoveField(
            model_name='transaction',
            name='account',
        ),
        migrations.RemoveField(
            model_name='transaction',
            name='journal_entry',
        ),
        migrations.RemoveField(
            model_name='incomingoperation',
            name='invoice_number',
        ),
        migrations.RemoveField(
            model_name='outgoingoperation',
            name='invoice_number',
        ),
        migrations.DeleteModel(
            name='Account',
        ),
        migrations.DeleteModel(
            name='AccountingPeriod',
        ),
        migrations.DeleteModel(
            name='AccountType',
        ),
        migrations.DeleteModel(
            name='Budget',
        ),
        migrations.DeleteModel(
            name='BudgetItem',
        ),
        migrations.DeleteModel(
            name='CashFlow',
        ),
        migrations.DeleteModel(
            name='Invoice',
        ),
        migrations.DeleteModel(
            name='InvoiceItem',
        ),
        migrations.DeleteModel(
            name='JournalEntry',
        ),
        migrations.DeleteModel(
            name='Payment',
        ),
        migrations.DeleteModel(
            name='PaymentMethod',
        ),
        migrations.DeleteModel(
            name='Transaction',
        ),
    ]
