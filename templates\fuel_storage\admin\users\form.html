{% extends 'fuel_storage/admin/base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item">
    <a href="{% url 'fuel_storage:admin:user_list' %}">إدارة المستخدمين</a>
</li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-{% if user_obj %}edit{% else %}plus{% endif %} text-primary"></i>
        {{ title }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'fuel_storage:admin:user_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i>
                العودة للقائمة
            </a>
            {% if user_obj %}
                <a href="{% url 'fuel_storage:admin:user_detail' user_obj.id %}" class="btn btn-outline-info">
                    <i class="fas fa-eye"></i>
                    عرض التفاصيل
                </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user"></i>
                    بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- معلومات أساسية -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                {{ form.username.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.username.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.username.help_text %}
                                <div class="form-text">{{ form.username.help_text }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.full_name.id_for_label }}" class="form-label">
                                {{ form.full_name.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.full_name }}
                            {% if form.full_name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.full_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.email.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.user_type.id_for_label }}" class="form-label">
                                {{ form.user_type.label }}
                                <span class="text-danger">*</span>
                            </label>
                            {{ form.user_type }}
                            {% if form.user_type.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.user_type.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                {{ form.phone_number.label }}
                            </label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.phone_number.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.department.id_for_label }}" class="form-label">
                                {{ form.department.label }}
                            </label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.department.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- كلمة المرور (للمستخدمين الجدد فقط) -->
                    {% if form.password1 %}
                        <hr class="my-4">
                        <h6 class="mb-3">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </h6>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">
                                    {{ form.password1.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password1.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.password1.help_text %}
                                    <div class="form-text">{{ form.password1.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">
                                    {{ form.password2.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password2.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.password2.help_text %}
                                    <div class="form-text">{{ form.password2.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- حالة المستخدم (للتعديل فقط) -->
                    {% if form.is_active %}
                        <hr class="my-4">
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {{ form.is_active.label }}
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.is_active.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <!-- أزرار الحفظ -->
                    <hr class="my-4">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                {% if user_obj %}تحديث البيانات{% else %}إنشاء المستخدم{% endif %}
                            </button>
                            <a href="{% url 'fuel_storage:admin:user_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if user_obj %}
                            <div>
                                <a href="{% url 'fuel_storage:admin:user_change_password' user_obj.id %}" class="btn btn-warning">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if user_obj %}
    <div class="row mt-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>تاريخ الإنشاء:</strong> {{ user_obj.date_joined|date:"Y-m-d H:i" }}</p>
                            <p><strong>آخر تسجيل دخول:</strong> {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول بعد" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الحالة:</strong> 
                                {% if user_obj.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </p>
                            <p><strong>مدير عام:</strong> 
                                {% if user_obj.is_superuser %}
                                    <span class="badge bg-danger">نعم</span>
                                {% else %}
                                    <span class="badge bg-secondary">لا</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
