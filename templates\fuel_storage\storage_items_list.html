{% extends 'base.html' %}

{% block title %}أصناف المخازن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">أصناف المخازن</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/storageitem/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة صنف مخزن جديد
        </a>
    </div>
</div>

{% if items %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>المخزن</th>
                    <th>الصنف</th>
                    <th>وحدة القياس</th>
                    <th>الرصيد الافتتاحي</th>
                    <th>الكمية الحالية</th>
                    <th>الحالة</th>
                    <th>آخر تحديث</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ item.storage.name }}</td>
                    <td>{{ item.category.name }}</td>
                    <td>{{ item.get_unit_of_measure_display }}</td>
                    <td class="number-display">{{ item.opening_balance|floatformat:3 }}</td>
                    <td class="number-display">{{ item.current_quantity|floatformat:3 }}</td>
                    <td>
                        {% if item.current_quantity <= 0 %}
                            <span class="badge bg-danger">نفدت الكمية</span>
                        {% elif item.current_quantity <= 100 %}
                            <span class="badge bg-warning text-dark">كمية منخفضة</span>
                        {% else %}
                            <span class="badge bg-success">متوفر</span>
                        {% endif %}
                    </td>
                    <td class="date-display">{{ item.updated_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <a href="/admin/fuel_storage/storageitem/{{ item.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد أصناف مخازن مسجلة حتى الآن.
    </div>
{% endif %}
{% endblock %}
