from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.core.validators import RegexValidator
from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary, Storage, StorageItem,
    IncomingOperation, OutgoingOperation, DamageOperation, StorageTransfer,
    IncomingReturn, OutgoingReturn, MaintenanceSchedule, QualityControl
)

# ================================
# نماذج المستخدمين
# ================================

class CustomUserCreationForm(UserCreationForm):
    """نموذج إنشاء مستخدم جديد"""
    full_name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الكامل'
        }),
        label='الاسم الكامل'
    )
    
    user_type = forms.ChoiceField(
        choices=CustomUser.USER_TYPES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='نوع المستخدم'
    )
    
    phone_number = forms.CharField(
        max_length=17,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+966xxxxxxxxx'
        }),
        label='رقم الهاتف'
    )
    
    department = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'القسم'
        }),
        label='القسم'
    )
    
    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'البريد الإلكتروني'
        }),
        label='البريد الإلكتروني'
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'full_name', 'email', 'user_type', 'phone_number', 'department', 'password1', 'password2')
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'اسم المستخدم'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور'
        })

class CustomUserUpdateForm(UserChangeForm):
    """نموذج تحديث بيانات المستخدم"""
    full_name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الكامل'
        }),
        label='الاسم الكامل'
    )
    
    user_type = forms.ChoiceField(
        choices=CustomUser.USER_TYPES,
        widget=forms.Select(attrs={'class': 'form-control'}),
        label='نوع المستخدم'
    )
    
    phone_number = forms.CharField(
        max_length=17,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+966xxxxxxxxx'
        }),
        label='رقم الهاتف'
    )
    
    department = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'القسم'
        }),
        label='القسم'
    )
    
    email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'البريد الإلكتروني'
        }),
        label='البريد الإلكتروني'
    )
    
    is_active = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='نشط'
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'full_name', 'email', 'user_type', 'phone_number', 'department', 'is_active')
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'اسم المستخدم'
        })
        # إزالة حقل كلمة المرور من النموذج
        if 'password' in self.fields:
            del self.fields['password']

# ================================
# نماذج الأصناف
# ================================

class CategoryForm(forms.ModelForm):
    """نموذج إدارة الأصناف"""

    class Meta:
        model = Category
        fields = ['name', 'code', 'description', 'minimum_stock_level', 'maximum_stock_level', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الصنف'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز الصنف'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الصنف'
            }),
            'minimum_stock_level': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001'
            }),
            'maximum_stock_level': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'name': 'اسم الصنف',
            'code': 'رمز الصنف',
            'description': 'الوصف',
            'minimum_stock_level': 'الحد الأدنى للمخزون',
            'maximum_stock_level': 'الحد الأقصى للمخزون',
            'is_active': 'نشط'
        }

# ================================
# نماذج المحطات
# ================================

class StationForm(forms.ModelForm):
    """نموذج إدارة المحطات"""
    
    class Meta:
        model = Station
        fields = ['name', 'code', 'address', 'manager_name', 'phone_number', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المحطة'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المحطة'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'عنوان المحطة'
            }),
            'manager_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم مدير المحطة'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966xxxxxxxxx'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'name': 'اسم المحطة',
            'code': 'رمز المحطة',
            'address': 'العنوان',
            'manager_name': 'اسم المدير',
            'phone_number': 'رقم الهاتف',
            'is_active': 'نشط'
        }

# ================================
# نماذج الموردين
# ================================

class SupplierForm(forms.ModelForm):
    """نموذج إدارة الموردين"""
    
    class Meta:
        model = Supplier
        fields = ['full_name', 'code', 'supplier_type', 'phone_number', 'email', 
                 'address', 'tax_number', 'credit_limit', 'payment_terms', 'rating', 'is_active']
        widgets = {
            'full_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المورد'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المورد'
            }),
            'supplier_type': forms.Select(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان'
            }),
            'tax_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الرقم الضريبي'
            }),
            'credit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'payment_terms': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'rating': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '5'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'full_name': 'اسم المورد',
            'code': 'رمز المورد',
            'supplier_type': 'نوع المورد',
            'phone_number': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'tax_number': 'الرقم الضريبي',
            'credit_limit': 'حد الائتمان',
            'payment_terms': 'شروط الدفع (أيام)',
            'rating': 'التقييم',
            'is_active': 'نشط'
        }

# ================================
# نماذج المستفيدين
# ================================

class BeneficiaryForm(forms.ModelForm):
    """نموذج إدارة المستفيدين"""

    class Meta:
        model = Beneficiary
        fields = ['full_name', 'code', 'beneficiary_type', 'phone_number', 'email',
                 'address', 'contact_person', 'credit_limit', 'is_active']
        widgets = {
            'full_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستفيد'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المستفيد'
            }),
            'beneficiary_type': forms.Select(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'العنوان'
            }),
            'contact_person': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الشخص المسؤول'
            }),
            'credit_limit': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'full_name': 'اسم المستفيد',
            'code': 'رمز المستفيد',
            'beneficiary_type': 'نوع المستفيد',
            'phone_number': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'contact_person': 'الشخص المسؤول',
            'credit_limit': 'حد الائتمان',
            'is_active': 'نشط'
        }

# ================================
# نماذج المخازن
# ================================

class StorageForm(forms.ModelForm):
    """نموذج إدارة المخازن"""

    class Meta:
        model = Storage
        fields = ['name', 'code', 'classification', 'location', 'capacity', 'keeper_name',
                 'phone_number', 'email', 'temperature_controlled', 'security_level', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المخزن'
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المخزن'
            }),
            'classification': forms.Select(attrs={'class': 'form-control'}),
            'location': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'موقع المخزن'
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001'
            }),
            'keeper_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم أمين المخزن'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+966xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'temperature_controlled': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'security_level': forms.Select(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'name': 'اسم المخزن',
            'code': 'رمز المخزن',
            'classification': 'التصنيف',
            'location': 'الموقع',
            'capacity': 'السعة الإجمالية',
            'keeper_name': 'أمين المخزن',
            'phone_number': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'temperature_controlled': 'مكيف الحرارة',
            'security_level': 'مستوى الأمان',
            'is_active': 'نشط'
        }
