from decimal import Decimal

def number_to_arabic_words(number):
    """تحويل الرقم إلى كلمات عربية"""
    """تحويل الرقم إلى كلمات عربية"""

    # قوائم الأرقام العربية
    ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة']
    tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون']
    teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر',
             'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر']
    hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة',
                'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة']

    def convert_hundreds(n):
        """تحويل الأرقام من 0 إلى 999"""
        if n == 0:
            return ''

        result = []

        # المئات
        if n >= 100:
            h = n // 100
            result.append(hundreds[h])
            n %= 100

        # العشرات والآحاد
        if n >= 20:
            t = n // 10
            result.append(tens[t])
            n %= 10
            if n > 0:
                result.append(ones[n])
        elif n >= 10:
            result.append(teens[n - 10])
        elif n > 0:
            result.append(ones[n])

        return ' '.join(result)

    def convert_number(num):
        """تحويل الرقم الكامل"""
        if num == 0:
            return 'صفر'

        # فصل الجزء الصحيح والعشري
        integer_part = int(num)
        decimal_part = round((num - integer_part) * 100)

        result = []

        # تحويل الجزء الصحيح
        if integer_part >= 1000000:
            millions = integer_part // 1000000
            result.append(convert_hundreds(millions))
            if millions == 1:
                result.append('مليون')
            elif millions == 2:
                result.append('مليونان')
            elif millions <= 10:
                result.append('ملايين')
            else:
                result.append('مليون')
            integer_part %= 1000000

        if integer_part >= 1000:
            thousands = integer_part // 1000
            result.append(convert_hundreds(thousands))
            if thousands == 1:
                result.append('ألف')
            elif thousands == 2:
                result.append('ألفان')
            elif thousands <= 10:
                result.append('آلاف')
            else:
                result.append('ألف')
            integer_part %= 1000

        if integer_part > 0:
            result.append(convert_hundreds(integer_part))

        # إضافة العملة
        integer_text = ' '.join(filter(None, result))
        if integer_text:
            integer_text += ' ريال'

        # تحويل الجزء العشري (الهللات)
        if decimal_part > 0:
            decimal_text = convert_hundreds(decimal_part)
            if decimal_text:
                if integer_text:
                    return f"{integer_text} و {decimal_text} هللة"
                else:
                    return f"{decimal_text} هللة"

        return integer_text if integer_text else 'صفر ريال'

    try:
        return convert_number(float(number))
    except (ValueError, TypeError):
        return 'رقم غير صحيح'
