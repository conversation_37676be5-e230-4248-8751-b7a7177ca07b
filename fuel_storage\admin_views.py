from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib.auth import update_session_auth_hash
from django.contrib.auth.forms import PasswordChangeForm

from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary, Storage, StorageItem,
    IncomingOperation, OutgoingOperation, DamageOperation, StorageTransfer,
    IncomingReturn, OutgoingReturn, MaintenanceSchedule, QualityControl
)
from .forms import (
    CustomUserCreationForm, CustomUserUpdateForm, CategoryForm, StationForm,
    SupplierForm, BeneficiaryForm, StorageForm
)

# ================================
# دوال مساعدة للصلاحيات
# ================================

def is_manager(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_authenticated and (user.user_type == 'manager' or user.is_superuser)

def is_manager_or_operator(user):
    """التحقق من أن المستخدم مدير أو مشغل"""
    return user.is_authenticated and (user.user_type in ['manager', 'operator'] or user.is_superuser)

# ================================
# لوحة التحكم الإدارية الرئيسية
# ================================

@login_required
@user_passes_test(is_manager)
def admin_dashboard(request):
    """لوحة التحكم الإدارية الرئيسية"""
    context = {
        'total_users': CustomUser.objects.count(),
        'active_users': CustomUser.objects.filter(is_active=True).count(),
        'total_categories': Category.objects.count(),
        'active_categories': Category.objects.filter(is_active=True).count(),
        'total_stations': Station.objects.count(),
        'active_stations': Station.objects.filter(is_active=True).count(),
        'total_suppliers': Supplier.objects.count(),
        'active_suppliers': Supplier.objects.filter(is_active=True).count(),
        'total_beneficiaries': Beneficiary.objects.count(),
        'active_beneficiaries': Beneficiary.objects.filter(is_active=True).count(),
        'total_storages': Storage.objects.count(),
        'active_storages': Storage.objects.filter(is_active=True).count(),
    }
    return render(request, 'fuel_storage/admin/dashboard.html', context)

# ================================
# إدارة المستخدمين
# ================================

@login_required
@user_passes_test(is_manager)
def user_list(request):
    """قائمة المستخدمين"""
    search_query = request.GET.get('search', '')
    user_type_filter = request.GET.get('user_type', '')
    status_filter = request.GET.get('status', '')
    
    users = CustomUser.objects.all().order_by('-date_joined')
    
    # تطبيق الفلاتر
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    if user_type_filter:
        users = users.filter(user_type=user_type_filter)
    
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    
    # التصفح
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'user_type_filter': user_type_filter,
        'status_filter': status_filter,
        'user_types': CustomUser.USER_TYPES,
    }
    return render(request, 'fuel_storage/admin/users/list.html', context)

@login_required
@user_passes_test(is_manager)
def user_create(request):
    """إنشاء مستخدم جديد"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'تم إنشاء المستخدم "{user.full_name}" بنجاح.')
            return redirect('fuel_storage:admin_user_list')
    else:
        form = CustomUserCreationForm()
    
    context = {'form': form, 'title': 'إنشاء مستخدم جديد'}
    return render(request, 'fuel_storage/admin/users/form.html', context)

@login_required
@user_passes_test(is_manager)
def user_detail(request, user_id):
    """تفاصيل المستخدم"""
    user = get_object_or_404(CustomUser, id=user_id)
    context = {'user_obj': user}
    return render(request, 'fuel_storage/admin/users/detail.html', context)

@login_required
@user_passes_test(is_manager)
def user_update(request, user_id):
    """تحديث بيانات المستخدم"""
    user = get_object_or_404(CustomUser, id=user_id)
    
    if request.method == 'POST':
        form = CustomUserUpdateForm(request.POST, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المستخدم "{user.full_name}" بنجاح.')
            return redirect('fuel_storage:admin_user_detail', user_id=user.id)
    else:
        form = CustomUserUpdateForm(instance=user)
    
    context = {'form': form, 'user_obj': user, 'title': 'تحديث بيانات المستخدم'}
    return render(request, 'fuel_storage/admin/users/form.html', context)

@login_required
@user_passes_test(is_manager)
def user_change_password(request, user_id):
    """تغيير كلمة مرور المستخدم"""
    user = get_object_or_404(CustomUser, id=user_id)
    
    if request.method == 'POST':
        form = PasswordChangeForm(user, request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تغيير كلمة مرور المستخدم "{user.full_name}" بنجاح.')
            return redirect('fuel_storage:admin_user_detail', user_id=user.id)
    else:
        form = PasswordChangeForm(user)
    
    context = {'form': form, 'user_obj': user, 'title': 'تغيير كلمة المرور'}
    return render(request, 'fuel_storage/admin/users/change_password.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def user_toggle_status(request, user_id):
    """تفعيل/إلغاء تفعيل المستخدم"""
    user = get_object_or_404(CustomUser, id=user_id)
    
    # منع المستخدم من إلغاء تفعيل نفسه
    if user == request.user:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكنك إلغاء تفعيل حسابك الخاص'
        })
    
    user.is_active = not user.is_active
    user.save()
    
    status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} المستخدم "{user.full_name}" بنجاح.')
    
    return JsonResponse({
        'success': True,
        'is_active': user.is_active,
        'message': f'{status} المستخدم بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def user_delete(request, user_id):
    """حذف المستخدم"""
    user = get_object_or_404(CustomUser, id=user_id)
    
    # منع المستخدم من حذف نفسه
    if user == request.user:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكنك حذف حسابك الخاص'
        })
    
    # منع حذف المستخدم إذا كان له عمليات مرتبطة
    if (user.incomingoperation_set.exists() or 
        user.outgoingoperation_set.exists() or
        user.damageoperation_set.exists() or
        user.storagetransfer_set.exists()):
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذا المستخدم لأنه مرتبط بعمليات في النظام'
        })
    
    user_name = user.full_name
    user.delete()
    messages.success(request, f'تم حذف المستخدم "{user_name}" بنجاح.')
    
    return JsonResponse({
        'success': True,
        'message': 'تم حذف المستخدم بنجاح'
    })

# ================================
# إدارة الأصناف
# ================================

@login_required
@user_passes_test(is_manager_or_operator)
def category_list(request):
    """قائمة الأصناف"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    
    categories = Category.objects.all().order_by('name')
    
    # تطبيق الفلاتر
    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    if status_filter == 'active':
        categories = categories.filter(is_active=True)
    elif status_filter == 'inactive':
        categories = categories.filter(is_active=False)
    
    # التصفح
    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
    }
    return render(request, 'fuel_storage/admin/categories/list.html', context)

@login_required
@user_passes_test(is_manager)
def category_create(request):
    """إنشاء صنف جديد"""
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'تم إنشاء الصنف "{category.name}" بنجاح.')
            return redirect('fuel_storage:admin_category_list')
    else:
        form = CategoryForm()
    
    context = {'form': form, 'title': 'إنشاء صنف جديد'}
    return render(request, 'fuel_storage/admin/categories/form.html', context)

@login_required
@user_passes_test(is_manager_or_operator)
def category_detail(request, category_id):
    """تفاصيل الصنف"""
    category = get_object_or_404(Category, id=category_id)
    
    # إحصائيات الصنف
    storage_items = StorageItem.objects.filter(category=category)
    total_quantity = sum(item.current_quantity for item in storage_items)
    total_value = sum(item.current_quantity * item.average_cost for item in storage_items)
    
    context = {
        'category': category,
        'storage_items': storage_items,
        'total_quantity': total_quantity,
        'total_value': total_value,
    }
    return render(request, 'fuel_storage/admin/categories/detail.html', context)

@login_required
@user_passes_test(is_manager)
def category_update(request, category_id):
    """تحديث بيانات الصنف"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات الصنف "{category.name}" بنجاح.')
            return redirect('fuel_storage:admin_category_detail', category_id=category.id)
    else:
        form = CategoryForm(instance=category)

    context = {'form': form, 'category': category, 'title': 'تحديث بيانات الصنف'}
    return render(request, 'fuel_storage/admin/categories/form.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def category_toggle_status(request, category_id):
    """تفعيل/إلغاء تفعيل الصنف"""
    category = get_object_or_404(Category, id=category_id)

    category.is_active = not category.is_active
    category.save()

    status = 'تم تفعيل' if category.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} الصنف "{category.name}" بنجاح.')

    return JsonResponse({
        'success': True,
        'is_active': category.is_active,
        'message': f'{status} الصنف بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def category_delete(request, category_id):
    """حذف الصنف"""
    category = get_object_or_404(Category, id=category_id)

    try:
        category_name = category.name
        category.delete()
        messages.success(request, f'تم حذف الصنف "{category_name}" بنجاح.')
        return JsonResponse({
            'success': True,
            'message': 'تم حذف الصنف بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذا الصنف لأنه مرتبط بعمليات في النظام'
        })

# ================================
# إدارة المحطات
# ================================

@login_required
@user_passes_test(is_manager_or_operator)
def station_list(request):
    """قائمة المحطات"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    stations = Station.objects.all().order_by('name')

    # تطبيق الفلاتر
    if search_query:
        stations = stations.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query)
        )

    if status_filter == 'active':
        stations = stations.filter(is_active=True)
    elif status_filter == 'inactive':
        stations = stations.filter(is_active=False)

    # التصفح
    paginator = Paginator(stations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
    }
    return render(request, 'fuel_storage/admin/stations/list.html', context)

@login_required
@user_passes_test(is_manager)
def station_create(request):
    """إنشاء محطة جديدة"""
    if request.method == 'POST':
        form = StationForm(request.POST)
        if form.is_valid():
            station = form.save()
            messages.success(request, f'تم إنشاء المحطة "{station.name}" بنجاح.')
            return redirect('fuel_storage:admin_station_list')
    else:
        form = StationForm()

    context = {'form': form, 'title': 'إنشاء محطة جديدة'}
    return render(request, 'fuel_storage/admin/stations/form.html', context)

@login_required
@user_passes_test(is_manager_or_operator)
def station_detail(request, station_id):
    """تفاصيل المحطة"""
    station = get_object_or_404(Station, id=station_id)

    # إحصائيات المحطة
    incoming_operations = station.incomingoperation_set.all()[:10]
    total_operations = station.incomingoperation_set.count()

    context = {
        'station': station,
        'incoming_operations': incoming_operations,
        'total_operations': total_operations,
    }
    return render(request, 'fuel_storage/admin/stations/detail.html', context)

@login_required
@user_passes_test(is_manager)
def station_update(request, station_id):
    """تحديث بيانات المحطة"""
    station = get_object_or_404(Station, id=station_id)

    if request.method == 'POST':
        form = StationForm(request.POST, instance=station)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المحطة "{station.name}" بنجاح.')
            return redirect('fuel_storage:admin_station_detail', station_id=station.id)
    else:
        form = StationForm(instance=station)

    context = {'form': form, 'station': station, 'title': 'تحديث بيانات المحطة'}
    return render(request, 'fuel_storage/admin/stations/form.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def station_toggle_status(request, station_id):
    """تفعيل/إلغاء تفعيل المحطة"""
    station = get_object_or_404(Station, id=station_id)

    station.is_active = not station.is_active
    station.save()

    status = 'تم تفعيل' if station.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} المحطة "{station.name}" بنجاح.')

    return JsonResponse({
        'success': True,
        'is_active': station.is_active,
        'message': f'{status} المحطة بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def station_delete(request, station_id):
    """حذف المحطة"""
    station = get_object_or_404(Station, id=station_id)

    try:
        station_name = station.name
        station.delete()
        messages.success(request, f'تم حذف المحطة "{station_name}" بنجاح.')
        return JsonResponse({
            'success': True,
            'message': 'تم حذف المحطة بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذه المحطة لأنها مرتبطة بعمليات في النظام'
        })

# ================================
# إدارة الموردين
# ================================

@login_required
@user_passes_test(is_manager_or_operator)
def supplier_list(request):
    """قائمة الموردين"""
    search_query = request.GET.get('search', '')
    supplier_type_filter = request.GET.get('supplier_type', '')
    status_filter = request.GET.get('status', '')

    suppliers = Supplier.objects.all().order_by('full_name')

    # تطبيق الفلاتر
    if search_query:
        suppliers = suppliers.filter(
            Q(full_name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    if supplier_type_filter:
        suppliers = suppliers.filter(supplier_type=supplier_type_filter)

    if status_filter == 'active':
        suppliers = suppliers.filter(is_active=True)
    elif status_filter == 'inactive':
        suppliers = suppliers.filter(is_active=False)

    # التصفح
    paginator = Paginator(suppliers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'supplier_type_filter': supplier_type_filter,
        'status_filter': status_filter,
        'supplier_types': Supplier.SUPPLIER_TYPES,
    }
    return render(request, 'fuel_storage/admin/suppliers/list.html', context)

@login_required
@user_passes_test(is_manager)
def supplier_create(request):
    """إنشاء مورد جديد"""
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            supplier = form.save()
            messages.success(request, f'تم إنشاء المورد "{supplier.full_name}" بنجاح.')
            return redirect('fuel_storage:admin:supplier_list')
    else:
        form = SupplierForm()

    context = {'form': form, 'title': 'إنشاء مورد جديد'}
    return render(request, 'fuel_storage/admin/suppliers/form.html', context)

@login_required
@user_passes_test(is_manager_or_operator)
def supplier_detail(request, supplier_id):
    """تفاصيل المورد"""
    supplier = get_object_or_404(Supplier, id=supplier_id)

    # إحصائيات المورد
    incoming_operations = supplier.incomingoperation_set.all()[:10]
    total_operations = supplier.incomingoperation_set.count()

    context = {
        'supplier': supplier,
        'incoming_operations': incoming_operations,
        'total_operations': total_operations,
    }
    return render(request, 'fuel_storage/admin/suppliers/detail.html', context)

@login_required
@user_passes_test(is_manager)
def supplier_update(request, supplier_id):
    """تحديث بيانات المورد"""
    supplier = get_object_or_404(Supplier, id=supplier_id)

    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المورد "{supplier.full_name}" بنجاح.')
            return redirect('fuel_storage:admin:supplier_detail', supplier_id=supplier.id)
    else:
        form = SupplierForm(instance=supplier)

    context = {'form': form, 'supplier': supplier, 'title': 'تحديث بيانات المورد'}
    return render(request, 'fuel_storage/admin/suppliers/form.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def supplier_toggle_status(request, supplier_id):
    """تفعيل/إلغاء تفعيل المورد"""
    supplier = get_object_or_404(Supplier, id=supplier_id)

    supplier.is_active = not supplier.is_active
    supplier.save()

    status = 'تم تفعيل' if supplier.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} المورد "{supplier.full_name}" بنجاح.')

    return JsonResponse({
        'success': True,
        'is_active': supplier.is_active,
        'message': f'{status} المورد بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def supplier_delete(request, supplier_id):
    """حذف المورد"""
    supplier = get_object_or_404(Supplier, id=supplier_id)

    try:
        supplier_name = supplier.full_name
        supplier.delete()
        messages.success(request, f'تم حذف المورد "{supplier_name}" بنجاح.')
        return JsonResponse({
            'success': True,
            'message': 'تم حذف المورد بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذا المورد لأنه مرتبط بعمليات في النظام'
        })

# ================================
# إدارة المستفيدين
# ================================

@login_required
@user_passes_test(is_manager_or_operator)
def beneficiary_list(request):
    """قائمة المستفيدين"""
    search_query = request.GET.get('search', '')
    beneficiary_type_filter = request.GET.get('beneficiary_type', '')
    status_filter = request.GET.get('status', '')

    beneficiaries = Beneficiary.objects.all().order_by('full_name')

    # تطبيق الفلاتر
    if search_query:
        beneficiaries = beneficiaries.filter(
            Q(full_name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    if beneficiary_type_filter:
        beneficiaries = beneficiaries.filter(beneficiary_type=beneficiary_type_filter)

    if status_filter == 'active':
        beneficiaries = beneficiaries.filter(is_active=True)
    elif status_filter == 'inactive':
        beneficiaries = beneficiaries.filter(is_active=False)

    # التصفح
    paginator = Paginator(beneficiaries, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'beneficiary_type_filter': beneficiary_type_filter,
        'status_filter': status_filter,
        'beneficiary_types': Beneficiary.BENEFICIARY_TYPES,
    }
    return render(request, 'fuel_storage/admin/beneficiaries/list.html', context)

@login_required
@user_passes_test(is_manager)
def beneficiary_create(request):
    """إنشاء مستفيد جديد"""
    if request.method == 'POST':
        form = BeneficiaryForm(request.POST)
        if form.is_valid():
            beneficiary = form.save()
            messages.success(request, f'تم إنشاء المستفيد "{beneficiary.full_name}" بنجاح.')
            return redirect('fuel_storage:admin:beneficiary_list')
    else:
        form = BeneficiaryForm()

    context = {'form': form, 'title': 'إنشاء مستفيد جديد'}
    return render(request, 'fuel_storage/admin/beneficiaries/form.html', context)

@login_required
@user_passes_test(is_manager_or_operator)
def beneficiary_detail(request, beneficiary_id):
    """تفاصيل المستفيد"""
    beneficiary = get_object_or_404(Beneficiary, id=beneficiary_id)

    # إحصائيات المستفيد
    outgoing_operations = beneficiary.outgoingoperation_set.all()[:10]
    total_operations = beneficiary.outgoingoperation_set.count()

    context = {
        'beneficiary': beneficiary,
        'outgoing_operations': outgoing_operations,
        'total_operations': total_operations,
    }
    return render(request, 'fuel_storage/admin/beneficiaries/detail.html', context)

@login_required
@user_passes_test(is_manager)
def beneficiary_update(request, beneficiary_id):
    """تحديث بيانات المستفيد"""
    beneficiary = get_object_or_404(Beneficiary, id=beneficiary_id)

    if request.method == 'POST':
        form = BeneficiaryForm(request.POST, instance=beneficiary)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المستفيد "{beneficiary.full_name}" بنجاح.')
            return redirect('fuel_storage:admin:beneficiary_detail', beneficiary_id=beneficiary.id)
    else:
        form = BeneficiaryForm(instance=beneficiary)

    context = {'form': form, 'beneficiary': beneficiary, 'title': 'تحديث بيانات المستفيد'}
    return render(request, 'fuel_storage/admin/beneficiaries/form.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def beneficiary_toggle_status(request, beneficiary_id):
    """تفعيل/إلغاء تفعيل المستفيد"""
    beneficiary = get_object_or_404(Beneficiary, id=beneficiary_id)

    beneficiary.is_active = not beneficiary.is_active
    beneficiary.save()

    status = 'تم تفعيل' if beneficiary.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} المستفيد "{beneficiary.full_name}" بنجاح.')

    return JsonResponse({
        'success': True,
        'is_active': beneficiary.is_active,
        'message': f'{status} المستفيد بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def beneficiary_delete(request, beneficiary_id):
    """حذف المستفيد"""
    beneficiary = get_object_or_404(Beneficiary, id=beneficiary_id)

    try:
        beneficiary_name = beneficiary.full_name
        beneficiary.delete()
        messages.success(request, f'تم حذف المستفيد "{beneficiary_name}" بنجاح.')
        return JsonResponse({
            'success': True,
            'message': 'تم حذف المستفيد بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذا المستفيد لأنه مرتبط بعمليات في النظام'
        })

# ================================
# إدارة المخازن
# ================================

@login_required
@user_passes_test(is_manager_or_operator)
def storage_list(request):
    """قائمة المخازن"""
    search_query = request.GET.get('search', '')
    classification_filter = request.GET.get('classification', '')
    status_filter = request.GET.get('status', '')

    storages = Storage.objects.all().order_by('name')

    # تطبيق الفلاتر
    if search_query:
        storages = storages.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(keeper_name__icontains=search_query)
        )

    if classification_filter:
        storages = storages.filter(classification=classification_filter)

    if status_filter == 'active':
        storages = storages.filter(is_active=True)
    elif status_filter == 'inactive':
        storages = storages.filter(is_active=False)

    # التصفح
    paginator = Paginator(storages, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'classification_filter': classification_filter,
        'status_filter': status_filter,
        'classifications': Storage.CLASSIFICATION_CHOICES,
    }
    return render(request, 'fuel_storage/admin/storages/list.html', context)

@login_required
@user_passes_test(is_manager)
def storage_create(request):
    """إنشاء مخزن جديد"""
    if request.method == 'POST':
        form = StorageForm(request.POST)
        if form.is_valid():
            storage = form.save()
            messages.success(request, f'تم إنشاء المخزن "{storage.name}" بنجاح.')
            return redirect('fuel_storage:admin:storage_list')
    else:
        form = StorageForm()

    context = {'form': form, 'title': 'إنشاء مخزن جديد'}
    return render(request, 'fuel_storage/admin/storages/form.html', context)

@login_required
@user_passes_test(is_manager_or_operator)
def storage_detail(request, storage_id):
    """تفاصيل المخزن"""
    storage = get_object_or_404(Storage, id=storage_id)

    # إحصائيات المخزن
    storage_items = storage.storageitem_set.all()
    total_items = storage_items.count()
    capacity_usage = storage.get_current_capacity_usage()

    context = {
        'storage': storage,
        'storage_items': storage_items,
        'total_items': total_items,
        'capacity_usage': capacity_usage,
    }
    return render(request, 'fuel_storage/admin/storages/detail.html', context)

@login_required
@user_passes_test(is_manager)
def storage_update(request, storage_id):
    """تحديث بيانات المخزن"""
    storage = get_object_or_404(Storage, id=storage_id)

    if request.method == 'POST':
        form = StorageForm(request.POST, instance=storage)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات المخزن "{storage.name}" بنجاح.')
            return redirect('fuel_storage:admin:storage_detail', storage_id=storage.id)
    else:
        form = StorageForm(instance=storage)

    context = {'form': form, 'storage': storage, 'title': 'تحديث بيانات المخزن'}
    return render(request, 'fuel_storage/admin/storages/form.html', context)

@login_required
@user_passes_test(is_manager)
@require_POST
def storage_toggle_status(request, storage_id):
    """تفعيل/إلغاء تفعيل المخزن"""
    storage = get_object_or_404(Storage, id=storage_id)

    storage.is_active = not storage.is_active
    storage.save()

    status = 'تم تفعيل' if storage.is_active else 'تم إلغاء تفعيل'
    messages.success(request, f'{status} المخزن "{storage.name}" بنجاح.')

    return JsonResponse({
        'success': True,
        'is_active': storage.is_active,
        'message': f'{status} المخزن بنجاح'
    })

@login_required
@user_passes_test(is_manager)
@require_POST
def storage_delete(request, storage_id):
    """حذف المخزن"""
    storage = get_object_or_404(Storage, id=storage_id)

    try:
        storage_name = storage.name
        storage.delete()
        messages.success(request, f'تم حذف المخزن "{storage_name}" بنجاح.')
        return JsonResponse({
            'success': True,
            'message': 'تم حذف المخزن بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف هذا المخزن لأنه مرتبط بعمليات في النظام'
        })

# ================================
# دوال مؤقتة للـ URLs المفقودة
# ================================

# هذه دوال مؤقتة لتجنب أخطاء الـ URLs
# سيتم استبدالها بالدوال الحقيقية لاحقاً

def storage_item_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إدارة أصناف المخازن'})

def storage_item_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة صنف مخزن'})

def storage_item_detail(request, item_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل صنف المخزن'})

def storage_item_update(request, item_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث صنف المخزن'})

def storage_item_delete(request, item_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

# العمليات
def incoming_operation_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'عمليات الوارد'})

def incoming_operation_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة عملية وارد'})

def incoming_operation_detail(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل عملية الوارد'})

def incoming_operation_update(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث عملية الوارد'})

def incoming_operation_delete(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def incoming_operation_lock(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def outgoing_operation_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'عمليات الصادر'})

def outgoing_operation_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة عملية صادر'})

def outgoing_operation_detail(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل عملية الصادر'})

def outgoing_operation_update(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث عملية الصادر'})

def outgoing_operation_delete(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def outgoing_operation_lock(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def damage_operation_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'عمليات التلف'})

def damage_operation_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة عملية تلف'})

def damage_operation_detail(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل عملية التلف'})

def damage_operation_update(request, operation_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث عملية التلف'})

def damage_operation_delete(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def damage_operation_lock(request, operation_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def storage_transfer_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'النقل المخزني'})

def storage_transfer_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة عملية نقل'})

def storage_transfer_detail(request, transfer_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل عملية النقل'})

def storage_transfer_update(request, transfer_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث عملية النقل'})

def storage_transfer_delete(request, transfer_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def storage_transfer_lock(request, transfer_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def incoming_return_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'مرتجعات الوارد'})

def incoming_return_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة مرتجع وارد'})

def incoming_return_detail(request, return_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل مرتجع الوارد'})

def incoming_return_update(request, return_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث مرتجع الوارد'})

def incoming_return_delete(request, return_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def incoming_return_lock(request, return_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def outgoing_return_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'مرتجعات الصادر'})

def outgoing_return_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة مرتجع صادر'})

def outgoing_return_detail(request, return_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل مرتجع الصادر'})

def outgoing_return_update(request, return_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث مرتجع الصادر'})

def outgoing_return_delete(request, return_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def outgoing_return_lock(request, return_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def maintenance_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'جدولة الصيانة'})

def maintenance_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة صيانة'})

def maintenance_detail(request, maintenance_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل الصيانة'})

def maintenance_update(request, maintenance_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث الصيانة'})

def maintenance_delete(request, maintenance_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def maintenance_complete(request, maintenance_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})

def quality_control_list(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'مراقبة الجودة'})

def quality_control_create(request):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'إضافة فحص جودة'})

def quality_control_detail(request, qc_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تفاصيل فحص الجودة'})

def quality_control_update(request, qc_id):
    return render(request, 'fuel_storage/admin/coming_soon.html', {'title': 'تحديث فحص الجودة'})

def quality_control_delete(request, qc_id):
    return JsonResponse({'success': False, 'message': 'قريباً'})
