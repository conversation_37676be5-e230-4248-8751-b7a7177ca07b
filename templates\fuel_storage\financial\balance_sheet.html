{% extends 'base.html' %}
{% load static %}
{% load financial_filters %}

{% block title %}الميزانية العمومية{% endblock %}

{% block extra_css %}
<style>
    .balance-sheet-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .balance-sheet-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .balance-sheet-title {
        font-size: 2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .balance-sheet-date {
        font-size: 1.1rem;
        color: #7f8c8d;
    }
    
    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .assets-section .section-header {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    .liabilities-section .section-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .equity-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .account-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        border-bottom: 1px solid #ecf0f1;
        transition: background-color 0.3s ease;
    }
    
    .account-row:hover {
        background-color: #f8f9fa;
    }
    
    .account-row:last-child {
        border-bottom: none;
    }
    
    .account-name {
        font-weight: 500;
        color: #2c3e50;
    }
    
    .account-code {
        font-family: monospace;
        background: #ecf0f1;
        padding: 3px 8px;
        border-radius: 15px;
        font-size: 0.85rem;
        color: #7f8c8d;
        margin-left: 10px;
    }
    
    .account-balance {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .balance-positive {
        color: #27ae60;
    }
    
    .balance-negative {
        color: #e74c3c;
    }
    
    .total-row {
        background: #f8f9fa;
        border-top: 2px solid #bdc3c7;
        font-weight: bold;
        font-size: 1.2rem;
    }
    
    .grand-total-row {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: bold;
        font-size: 1.3rem;
        border-radius: 10px;
        margin-top: 10px;
    }
    
    .balance-equation {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        margin: 30px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .equation-text {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .equation-values {
        font-size: 1.5rem;
        font-weight: bold;
        color: #e17055;
        margin-top: 10px;
    }
    
    .print-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 25px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .print-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .balance-sheet-container {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-balance-scale me-2"></i>
                        الميزانية العمومية
                    </h1>
                    <p class="text-muted">عرض الوضع المالي للشركة في تاريخ محدد</p>
                </div>
                <div>
                    <button onclick="window.print()" class="print-button me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="row mb-4 no-print">
        <div class="col-md-4">
            <form method="get" class="d-flex">
                <input type="date" name="date" class="form-control me-2" value="{{ report_date|date:'Y-m-d' }}">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>

    <!-- Balance Sheet -->
    <div class="balance-sheet-container">
        <div class="balance-sheet-header">
            <div class="balance-sheet-title">الميزانية العمومية</div>
            <div class="balance-sheet-date">كما في {{ report_date|date:"j F Y" }}</div>
        </div>

        <div class="row">
            <!-- Assets -->
            <div class="col-lg-6">
                <div class="assets-section">
                    <div class="section-header">
                        <i class="fas fa-coins me-2"></i>
                        الأصول
                    </div>
                    
                    {% for asset in assets %}
                    <div class="account-row">
                        <div>
                            <span class="account-name">{{ asset.account.name }}</span>
                            <span class="account-code">{{ asset.account.code }}</span>
                        </div>
                        <div class="account-balance balance-positive">
                            {{ asset.balance|floatformat:2 }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        لا توجد أصول
                    </div>
                    {% endfor %}
                    
                    <div class="account-row total-row">
                        <div class="account-name">إجمالي الأصول</div>
                        <div class="account-balance balance-positive">
                            {{ total_assets|floatformat:2 }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liabilities and Equity -->
            <div class="col-lg-6">
                <!-- Liabilities -->
                <div class="liabilities-section mb-4">
                    <div class="section-header">
                        <i class="fas fa-credit-card me-2"></i>
                        الخصوم
                    </div>
                    
                    {% for liability in liabilities %}
                    <div class="account-row">
                        <div>
                            <span class="account-name">{{ liability.account.name }}</span>
                            <span class="account-code">{{ liability.account.code }}</span>
                        </div>
                        <div class="account-balance balance-negative">
                            {{ liability.balance|floatformat:2 }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        لا توجد خصوم
                    </div>
                    {% endfor %}
                    
                    <div class="account-row total-row">
                        <div class="account-name">إجمالي الخصوم</div>
                        <div class="account-balance balance-negative">
                            {{ total_liabilities|floatformat:2 }}
                        </div>
                    </div>
                </div>

                <!-- Equity -->
                <div class="equity-section">
                    <div class="section-header">
                        <i class="fas fa-chart-pie me-2"></i>
                        حقوق الملكية
                    </div>
                    
                    {% for equity_item in equity %}
                    <div class="account-row">
                        <div>
                            <span class="account-name">{{ equity_item.account.name }}</span>
                            <span class="account-code">{{ equity_item.account.code }}</span>
                        </div>
                        <div class="account-balance balance-positive">
                            {{ equity_item.balance|floatformat:2 }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        لا توجد حقوق ملكية
                    </div>
                    {% endfor %}
                    
                    <div class="account-row total-row">
                        <div class="account-name">إجمالي حقوق الملكية</div>
                        <div class="account-balance balance-positive">
                            {{ total_equity|floatformat:2 }}
                        </div>
                    </div>
                </div>

                <!-- Total Liabilities and Equity -->
                <div class="account-row grand-total-row">
                    <div class="account-name">إجمالي الخصوم وحقوق الملكية</div>
                    <div class="account-balance">
                        {{ total_liabilities_equity|floatformat:2 }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Balance Equation -->
    <div class="balance-equation">
        <div class="equation-text">معادلة الميزانية</div>
        <div class="equation-values">
            الأصول ({{ total_assets|floatformat:2 }}) = 
            الخصوم ({{ total_liabilities|floatformat:2 }}) + 
            حقوق الملكية ({{ total_equity|floatformat:2 }})
        </div>
        {% if total_assets == total_liabilities_equity %}
        <div class="mt-3">
            <span class="badge bg-success fs-6">
                <i class="fas fa-check me-2"></i>
                الميزانية متوازنة
            </span>
        </div>
        {% else %}
        <div class="mt-3">
            <span class="badge bg-danger fs-6">
                <i class="fas fa-exclamation-triangle me-2"></i>
                الميزانية غير متوازنة - فرق: {{ total_assets|subtract:total_liabilities_equity|floatformat:2 }}
            </span>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للصفوف
        const rows = document.querySelectorAll('.account-row:not(.total-row):not(.grand-total-row)');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
                this.style.backgroundColor = '#f1f2f6';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.backgroundColor = '';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `الميزانية العمومية - {{ report_date|date:"Y/m/d" }}`;
    });
</script>
{% endblock %}
