# دليل نظام الإدارة الشامل
# Comprehensive Admin System Guide

## نظرة عامة | Overview

تم تطوير نظام إدارة شامل بواجهات مخصصة لجميع العمليات في نظام إدارة مخازن المحروقات، بدلاً من الاعتماد على لوحة الإدارة الافتراضية في Django.

A comprehensive admin system has been developed with custom interfaces for all operations in the Fuel Storage Management System, instead of relying on Django's default admin panel.

## كيفية تشغيل النظام | How to Run the System

### الطريقة السريعة | Quick Method
```bash
# تشغيل الملف المساعد
run_server.bat
```

### الطريقة اليدوية | Manual Method
```bash
# تفعيل البيئة الافتراضية
venv\Scripts\activate

# فحص النظام
python manage.py check

# تطبيق التحديثات
python manage.py migrate

# تشغيل الخادم
python manage.py runserver 8000
```

## الوصول للنظام | System Access

### النظام الرئيسي | Main System
- **الرابط**: http://localhost:8000/
- **الوصف**: النظام الأساسي لإدارة العمليات اليومية

### لوحة التحكم الإدارية | Admin Dashboard
- **الرابط**: http://localhost:8000/admin/
- **الوصف**: نظام الإدارة الشامل للبيانات الأساسية

## الميزات المتاحة | Available Features

### ✅ متاح حالياً | Currently Available

#### 1. إدارة المستخدمين | User Management
- **المسار**: `/admin/users/`
- **الميزات**:
  - إضافة مستخدمين جدد
  - تعديل بيانات المستخدمين
  - تفعيل/إلغاء تفعيل المستخدمين
  - حذف المستخدمين (مع الحماية)
  - تغيير كلمات المرور
  - البحث والفلترة المتقدمة

#### 2. إدارة الأصناف | Category Management
- **المسار**: `/admin/categories/`
- **الميزات**:
  - إضافة أصناف جديدة
  - تعديل بيانات الأصناف
  - عرض إحصائيات الأصناف
  - تفعيل/إلغاء تفعيل الأصناف
  - حذف الأصناف

#### 3. إدارة المحطات | Station Management
- **المسار**: `/admin/stations/`
- **الميزات**:
  - إضافة محطات جديدة
  - تعديل بيانات المحطات
  - عرض إحصائيات المحطات
  - إدارة معلومات المديرين

#### 4. إدارة الموردين | Supplier Management
- **المسار**: `/admin/suppliers/`
- **الميزات**:
  - إضافة موردين جدد
  - تعديل بيانات الموردين
  - إدارة حدود الائتمان
  - تقييم الموردين
  - عرض تاريخ العمليات

#### 5. إدارة المستفيدين | Beneficiary Management
- **المسار**: `/admin/beneficiaries/`
- **الميزات**:
  - إضافة مستفيدين جدد
  - تعديل بيانات المستفيدين
  - إدارة حدود الائتمان
  - عرض تاريخ العمليات

#### 6. إدارة المخازن | Storage Management
- **المسار**: `/admin/storages/`
- **الميزات**:
  - إضافة مخازن جديدة
  - تعديل بيانات المخازن
  - إدارة السعات والمواقع
  - عرض إحصائيات المخزون

### ⏳ قيد التطوير | Under Development

- إدارة أصناف المخازن
- إدارة عمليات الوارد والصادر
- إدارة عمليات التلف والنقل
- إدارة المرتجعات
- إدارة الصيانة
- إدارة مراقبة الجودة

## نظام الصلاحيات | Permission System

### المدير | Manager
- **الصلاحيات**: جميع العمليات (إضافة، تعديل، حذف، عرض)
- **المتطلبات**: `user_type = 'manager'` أو `is_superuser = True`

### المشغل | Operator
- **الصلاحيات**: عرض وبحث فقط
- **المتطلبات**: `user_type = 'operator'`

### المراقب | Viewer
- **الصلاحيات**: عرض فقط
- **المتطلبات**: `user_type = 'viewer'`

## الميزات التقنية | Technical Features

### التصميم | Design
- **Framework**: Bootstrap 5
- **Icons**: Font Awesome 6
- **Responsive**: متجاوب مع جميع الأجهزة

### الأمان | Security
- **CSRF Protection**: حماية من هجمات CSRF
- **Permission System**: نظام صلاحيات محكم
- **Input Validation**: التحقق من صحة البيانات

### تجربة المستخدم | User Experience
- **Search & Filter**: بحث وفلترة متقدمة
- **Pagination**: تصفح الصفحات
- **AJAX Operations**: عمليات سريعة بدون إعادة تحميل
- **Confirmation Dialogs**: تأكيدات للعمليات الحساسة
- **Success/Error Messages**: رسائل واضحة للمستخدم

## استكشاف الأخطاء | Troubleshooting

### مشكلة Django غير مثبت
```bash
# تفعيل البيئة الافتراضية أولاً
venv\Scripts\activate

# ثم تثبيت Django
pip install django
```

### مشكلة في قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
python manage.py migrate
```

### مشكلة في الصلاحيات
```bash
# إنشاء مستخدم مدير
python manage.py createsuperuser
```

## الدعم | Support

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة:
- ملف `TROUBLESHOOTING.md`
- سجلات النظام في مجلد `logs/`
- رسائل الخطأ في المتصفح

For help or to report issues, please check:
- `TROUBLESHOOTING.md` file
- System logs in `logs/` folder
- Browser error messages
