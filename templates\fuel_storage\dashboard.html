{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة مخازن المحروقات{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        border-radius: 0 0 20px 20px;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-color);
    }

    .stats-card.primary::before { background: linear-gradient(45deg, #667eea, #764ba2); }
    .stats-card.success::before { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
    .stats-card.info::before { background: linear-gradient(45deg, #3498db, #2980b9); }
    .stats-card.warning::before { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .stats-card.danger::before { background: linear-gradient(45deg, #e74c3c, #c0392b); }
    .stats-card.secondary::before { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-icon.primary { background: linear-gradient(45deg, #667eea, #764ba2); }
    .stats-icon.success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
    .stats-icon.info { background: linear-gradient(45deg, #3498db, #2980b9); }
    .stats-icon.warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
    .stats-icon.danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
    .stats-icon.secondary { background: linear-gradient(45deg, #95a5a6, #7f8c8d); }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0.5rem 0;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .activity-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .activity-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .activity-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .activity-table {
        margin: 0;
    }

    .activity-table th {
        background: #f8f9fa;
        border: none;
        color: #6c757d;
        font-weight: 600;
        padding: 1rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .activity-table td {
        padding: 1rem;
        border-top: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .activity-table tbody tr:hover {
        background: #f8f9fa;
    }

    .welcome-text {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
        .dashboard-header {
            margin: -1rem -1rem 1.5rem -1rem;
            padding: 1.5rem 0;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stats-number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header text-center">
    <div class="container">
        <h1 class="dashboard-title">مرحباً بك في لوحة التحكم</h1>
        <p class="welcome-text">نظام إدارة مخازن المحروقات المتطور</p>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-5">
    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(231, 76, 60, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-users"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_users }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">المستخدمون</div>
                <div style="font-size: 0.8rem; opacity: 0.7;">نشط: {{ active_users }}</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_storages }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">إجمالي المخازن</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(67, 233, 123, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-tags"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_categories }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">إجمالي الأصناف</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(52, 152, 219, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-truck"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_suppliers }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">الموردون</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(149, 165, 166, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-users"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_beneficiaries }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">المستفيدون</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(243, 156, 18, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_incoming_operations }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">عمليات الوارد</div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-lg-4 col-md-6 mb-4">
        <div class="card h-100" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(231, 76, 60, 0.3);">
            <div class="card-body text-center p-4">
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div style="font-size: 2.5rem; font-weight: 700; margin: 0.5rem 0;">{{ total_outgoing_operations }}</div>
                <div style="font-size: 0.9rem; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">عمليات الصادر</div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card" style="border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); border: none; overflow: hidden;">
            <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); padding: 1.5rem; color: white;">
                <h5 style="margin: 0; font-weight: 700; display: flex; align-items: center; gap: 0.75rem;">
                    <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    أحدث عمليات الوارد
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table" style="margin: 0; border-radius: 0;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                    <th style="border: none; padding: 1rem; font-weight: 700;">الرقم الورقي</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">المخزن</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">المورد</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_incoming %}
                                <tr style="transition: all 0.3s ease;">
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <span style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">{{ operation.paper_number }}</span>
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-warehouse" style="color: #43e97b; margin-left: 0.5rem;"></i>
                                        {{ operation.storage.name }}
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-truck" style="color: #43e97b; margin-left: 0.5rem;"></i>
                                        {{ operation.supplier.full_name }}
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-calendar" style="color: #43e97b; margin-left: 0.5rem;"></i>
                                        {{ operation.operation_date|date:"Y-m-d" }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x" style="color: #43e97b; opacity: 0.5; margin-bottom: 1rem;"></i>
                        <p style="color: #6c757d;">لا توجد عمليات وارد حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card" style="border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); border: none; overflow: hidden;">
            <div style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); padding: 1.5rem; color: white;">
                <h5 style="margin: 0; font-weight: 700; display: flex; align-items: center; gap: 0.75rem;">
                    <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    أحدث عمليات الصادر
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table" style="margin: 0; border-radius: 0;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white;">
                                    <th style="border: none; padding: 1rem; font-weight: 700;">الرقم الورقي</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">المخزن</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">المستفيد</th>
                                    <th style="border: none; padding: 1rem; font-weight: 700;">التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_outgoing %}
                                <tr style="transition: all 0.3s ease;">
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <span style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem; font-weight: 600;">{{ operation.paper_number }}</span>
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-warehouse" style="color: #e74c3c; margin-left: 0.5rem;"></i>
                                        {{ operation.storage.name }}
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-users" style="color: #e74c3c; margin-left: 0.5rem;"></i>
                                        {{ operation.beneficiary.full_name }}
                                    </td>
                                    <td style="padding: 1rem; border-top: 1px solid #f1f3f4;">
                                        <i class="fas fa-calendar" style="color: #e74c3c; margin-left: 0.5rem;"></i>
                                        {{ operation.operation_date|date:"Y-m-d" }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x" style="color: #e74c3c; opacity: 0.5; margin-bottom: 1rem;"></i>
                        <p style="color: #6c757d;">لا توجد عمليات صادر حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- قسم الإدارة -->
{% if user.user_type == 'manager' or user.is_superuser %}
<div class="row mb-5">
    <div class="col-12">
        <div class="card" style="border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); border: none; overflow: hidden;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 2rem; color: white;">
                <h3 style="margin: 0; font-weight: 700; display: flex; align-items: center; gap: 1rem;">
                    <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-cogs"></i>
                    </div>
                    لوحة الإدارة
                </h3>
                <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">إدارة جميع عناصر النظام من مكان واحد</p>
            </div>
            <div class="card-body p-4">
                <div class="row">
                    <!-- إدارة المستخدمين -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="{% url 'fuel_storage:manage_users' %}" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #e74c3c; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة المستخدمين</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">إضافة وتعديل وحذف المستخدمين</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        إجمالي: {{ total_users }} | نشط: {{ active_users }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- إدارة الأصناف -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="{% url 'fuel_storage:manage_categories' %}" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #43e97b; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-tags"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة الأصناف</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">إضافة وتعديل أصناف المحروقات</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        إجمالي: {{ total_categories }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- إدارة الموردين -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="#" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #3498db; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة الموردين</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">إدارة الموردين وحدود الائتمان</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        إجمالي: {{ total_suppliers }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- إدارة المستفيدين -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="#" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #f39c12; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-users-cog"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة المستفيدين</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">إدارة المستفيدين من الخدمات</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        إجمالي: {{ total_beneficiaries }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- إدارة المخازن -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="#" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #9b59b6; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-warehouse"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة المخازن</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">إدارة المخازن والسعات</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        إجمالي: {{ total_storages }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>

                    <!-- إدارة العمليات -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <a href="#" class="text-decoration-none">
                            <div class="card h-100" style="border: 2px solid #1abc9c; border-radius: 15px; transition: all 0.3s ease; background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%); color: white;">
                                <div class="card-body text-center p-4">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; font-size: 24px;">
                                        <i class="fas fa-exchange-alt"></i>
                                    </div>
                                    <h5 style="font-weight: 700; margin-bottom: 0.5rem;">إدارة العمليات</h5>
                                    <p style="opacity: 0.9; margin: 0; font-size: 0.9rem;">عمليات الوارد والصادر والتلف</p>
                                    <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.8;">
                                        وارد: {{ total_incoming_operations }} | صادر: {{ total_outgoing_operations }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
