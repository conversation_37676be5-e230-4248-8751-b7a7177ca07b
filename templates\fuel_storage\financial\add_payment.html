{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة دفعة جديدة{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .form-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .form-title {
        font-size: 2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .form-subtitle {
        color: #7f8c8d;
        font-size: 1.1rem;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .section-title {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-left: 10px;
        color: #667eea;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }
    
    .payment-type-cards {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .payment-type-card {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .payment-type-card:hover {
        border-color: #667eea;
        background: #f8f9fa;
    }
    
    .payment-type-card.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .payment-type-card .icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    .payment-type-card .title {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .reference-section {
        display: none;
    }
    
    .reference-section.active {
        display: block;
    }
    
    .alert-info {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        border: none;
        border-radius: 10px;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة دفعة جديدة
                    </h1>
                    <p class="text-muted">إنشاء دفعة مقبوضة أو مدفوعة جديدة</p>
                </div>
                <div>
                    <a href="{% url 'fuel_storage:payments_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة المدفوعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <div class="form-title">إضافة دفعة جديدة</div>
                    <div class="form-subtitle">املأ البيانات أدناه لإنشاء دفعة جديدة</div>
                </div>

                <form method="post" id="paymentForm">
                    {% csrf_token %}
                    
                    <!-- نوع الدفعة -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-exchange-alt"></i>
                            نوع الدفعة
                        </div>
                        
                        <div class="payment-type-cards">
                            <div class="payment-type-card" data-type="received">
                                <div class="icon">
                                    <i class="fas fa-arrow-down" style="color: #27ae60;"></i>
                                </div>
                                <div class="title">مقبوض</div>
                                <div class="text-muted">أموال مستلمة</div>
                            </div>
                            
                            <div class="payment-type-card" data-type="paid">
                                <div class="icon">
                                    <i class="fas fa-arrow-up" style="color: #e74c3c;"></i>
                                </div>
                                <div class="title">مدفوع</div>
                                <div class="text-muted">أموال مدفوعة</div>
                            </div>
                        </div>
                        
                        <input type="hidden" name="payment_type" id="payment_type" required>
                    </div>

                    <!-- تفاصيل الدفعة -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-info-circle"></i>
                            تفاصيل الدفعة
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ *</label>
                                <input type="number" step="0.01" class="form-control" id="amount" name="amount" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_date" class="form-label">تاريخ الدفع *</label>
                                <input type="date" class="form-control" id="payment_date" name="payment_date" 
                                       value="{{ today|date:'Y-m-d' }}" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">طريقة الدفع *</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    {% for method in payment_methods %}
                                        <option value="{{ method.id }}">{{ method.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="reference_number" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                       placeholder="رقم الشيك، رقم التحويل، إلخ">
                            </div>
                        </div>
                    </div>

                    <!-- المرجع -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-link"></i>
                            ربط الدفعة
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك ربط الدفعة بفاتورة موجودة أو عميل/مورد مباشرة
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="invoice" class="form-label">الفاتورة</label>
                                <select class="form-select" id="invoice" name="invoice">
                                    <option value="">اختر فاتورة</option>
                                    {% for invoice in invoices %}
                                        <option value="{{ invoice.id }}">
                                            {{ invoice.invoice_number }} - {{ invoice.total_amount }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3 reference-section" id="supplier-section">
                                <label for="supplier" class="form-label">المورد</label>
                                <select class="form-select" id="supplier" name="supplier">
                                    <option value="">اختر مورد</option>
                                    {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4 mb-3 reference-section" id="beneficiary-section">
                                <label for="beneficiary" class="form-label">المستفيد</label>
                                <select class="form-select" id="beneficiary" name="beneficiary">
                                    <option value="">اختر مستفيد</option>
                                    {% for beneficiary in beneficiaries %}
                                        <option value="{{ beneficiary.id }}">{{ beneficiary.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="form-section">
                        <div class="section-title">
                            <i class="fas fa-sticky-note"></i>
                            ملاحظات
                        </div>
                        
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول الدفعة..."></textarea>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn-submit">
                                <i class="fas fa-save me-2"></i>
                                حفظ الدفعة
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{% url 'fuel_storage:payments_list' %}" class="btn-cancel">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('payment_date').value = today;
        
        // التعامل مع اختيار نوع الدفعة
        const paymentTypeCards = document.querySelectorAll('.payment-type-card');
        const paymentTypeInput = document.getElementById('payment_type');
        const supplierSection = document.getElementById('supplier-section');
        const beneficiarySection = document.getElementById('beneficiary-section');
        
        paymentTypeCards.forEach(card => {
            card.addEventListener('click', function() {
                // إزالة التحديد من جميع البطاقات
                paymentTypeCards.forEach(c => c.classList.remove('selected'));
                
                // تحديد البطاقة المختارة
                this.classList.add('selected');
                
                // تعيين القيمة
                const type = this.dataset.type;
                paymentTypeInput.value = type;
                
                // إظهار/إخفاء أقسام المرجع
                if (type === 'received') {
                    beneficiarySection.classList.add('active');
                    supplierSection.classList.remove('active');
                } else if (type === 'paid') {
                    supplierSection.classList.add('active');
                    beneficiarySection.classList.remove('active');
                }
            });
        });
        
        // التحقق من صحة النموذج
        const form = document.getElementById('paymentForm');
        form.addEventListener('submit', function(e) {
            if (!paymentTypeInput.value) {
                e.preventDefault();
                alert('يرجى اختيار نوع الدفعة');
                return false;
            }
            
            const amount = document.getElementById('amount').value;
            if (!amount || parseFloat(amount) <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ صحيح');
                return false;
            }
        });
        
        // تأثيرات تفاعلية
        const formControls = document.querySelectorAll('.form-control, .form-select');
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });
            
            control.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
