{% extends 'base.html' %}

{% block title %}تقرير حالة المخزن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">تقرير حالة المخزن</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'fuel_storage:reports_dashboard' %}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للتقارير
        </a>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">اختيار المخزن</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <label for="storage_id" class="form-label">المخزن</label>
                <select name="storage_id" id="storage_id" class="form-select" required onchange="this.form.submit()">
                    <option value="">اختر المخزن</option>
                    {% for storage in storages %}
                        <option value="{{ storage.id }}" {% if selected_storage and selected_storage.id == storage.id %}selected{% endif %}>
                            {{ storage.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6">
                {% if selected_storage %}
                <label class="form-label">تصدير التقرير</label>
                <div class="btn-group d-block" role="group">
                    <a href="?storage_id={{ selected_storage.id }}&export=pdf" 
                       class="btn btn-outline-danger" data-export="pdf">
                        <i class="fas fa-file-pdf me-1"></i>PDF
                    </a>
                    <a href="?storage_id={{ selected_storage.id }}&export=excel" 
                       class="btn btn-outline-success" data-export="excel">
                        <i class="fas fa-file-excel me-1"></i>Excel
                    </a>
                </div>
                {% endif %}
            </div>
        </form>
    </div>
</div>

{% if selected_storage %}
    <!-- معلومات المخزن -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">معلومات المخزن: {{ selected_storage.name }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>التصنيف:</strong> {{ selected_storage.get_classification_display }}
                </div>
                <div class="col-md-3">
                    <strong>أمين المخزن:</strong> {{ selected_storage.keeper_name }}
                </div>
                <div class="col-md-3">
                    <strong>الهاتف:</strong> {{ selected_storage.phone_number }}
                </div>
                <div class="col-md-3">
                    <strong>تاريخ الإنشاء:</strong> {{ selected_storage.created_at|date:"Y-m-d" }}
                </div>
            </div>
        </div>
    </div>

    <!-- أصناف المخزن -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">أصناف المخزن والكميات الحالية</h5>
        </div>
        <div class="card-body">
            {% if storage_items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>وحدة القياس</th>
                                <th>الرصيد الافتتاحي</th>
                                <th>الكمية الحالية</th>
                                <th>آخر تحديث</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in storage_items %}
                            <tr>
                                <td>{{ item.category.name }}</td>
                                <td>{{ item.get_unit_of_measure_display }}</td>
                                <td class="number-display">{{ item.opening_balance|floatformat:3 }}</td>
                                <td class="number-display">{{ item.current_quantity|floatformat:3 }}</td>
                                <td class="date-display">{{ item.updated_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if item.current_quantity <= 0 %}
                                        <span class="badge bg-danger">نفدت الكمية</span>
                                    {% elif item.current_quantity <= 100 %}
                                        <span class="badge bg-warning text-dark">كمية منخفضة</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد أصناف في هذا المخزن حتى الآن.
                </div>
            {% endif %}
        </div>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        يرجى اختيار مخزن لعرض التقرير الخاص به.
    </div>
{% endif %}
{% endblock %}
