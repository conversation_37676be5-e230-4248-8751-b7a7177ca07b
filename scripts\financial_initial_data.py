#!/usr/bin/env python
"""
سكريبت لإضافة البيانات الأولية للنظام المالي
"""

import os
import sys
import django
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuel_storage_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from fuel_storage.models import (
    AccountType, Account, AccountingPeriod, PaymentMethod,
    CustomUser
)

User = get_user_model()

def create_account_types():
    """إنشاء أنواع الحسابات الأساسية"""
    account_types_data = [
        {'name': 'الأصول المتداولة', 'code': '1100', 'account_type': 'asset'},
        {'name': 'الأصول الثابتة', 'code': '1200', 'account_type': 'asset'},
        {'name': 'الخصوم المتداولة', 'code': '2100', 'account_type': 'liability'},
        {'name': 'الخصوم طويلة الأجل', 'code': '2200', 'account_type': 'liability'},
        {'name': 'رأس المال', 'code': '3100', 'account_type': 'equity'},
        {'name': 'الأرباح المحتجزة', 'code': '3200', 'account_type': 'equity'},
        {'name': 'إيرادات المبيعات', 'code': '4100', 'account_type': 'revenue'},
        {'name': 'إيرادات أخرى', 'code': '4200', 'account_type': 'revenue'},
        {'name': 'مصروفات تشغيلية', 'code': '5100', 'account_type': 'expense'},
        {'name': 'مصروفات إدارية', 'code': '5200', 'account_type': 'expense'},
        {'name': 'تكلفة البضاعة المباعة', 'code': '6100', 'account_type': 'cost_of_goods'},
    ]
    
    print("إنشاء أنواع الحسابات...")
    for data in account_types_data:
        account_type, created = AccountType.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        if created:
            print(f"  ✓ تم إنشاء نوع الحساب: {account_type.name}")
        else:
            print(f"  - نوع الحساب موجود: {account_type.name}")

def create_accounts():
    """إنشاء الحسابات الأساسية"""
    # الحصول على مستخدم افتراضي
    try:
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_user(
                username='admin',
                password='admin123',
                full_name='مدير النظام',
                user_type='manager',
                is_superuser=True,
                is_staff=True
            )
    except:
        admin_user = User.objects.first()
    
    accounts_data = [
        # الأصول المتداولة
        {'name': 'النقدية', 'code': '1110', 'account_type_code': '1100', 'opening_balance': 50000},
        {'name': 'البنك - الحساب الجاري', 'code': '1120', 'account_type_code': '1100', 'opening_balance': 100000},
        {'name': 'العملاء', 'code': '1130', 'account_type_code': '1100', 'opening_balance': 0},
        {'name': 'المخزون', 'code': '1140', 'account_type_code': '1100', 'opening_balance': 200000},
        {'name': 'مصروفات مدفوعة مقدماً', 'code': '1150', 'account_type_code': '1100', 'opening_balance': 0},
        
        # الأصول الثابتة
        {'name': 'الأراضي والمباني', 'code': '1210', 'account_type_code': '1200', 'opening_balance': 500000},
        {'name': 'المعدات والآلات', 'code': '1220', 'account_type_code': '1200', 'opening_balance': 300000},
        {'name': 'وسائل النقل', 'code': '1230', 'account_type_code': '1200', 'opening_balance': 150000},
        {'name': 'مجمع الإهلاك', 'code': '1290', 'account_type_code': '1200', 'opening_balance': -50000},
        
        # الخصوم المتداولة
        {'name': 'الموردون', 'code': '2110', 'account_type_code': '2100', 'opening_balance': 0},
        {'name': 'مصروفات مستحقة', 'code': '2120', 'account_type_code': '2100', 'opening_balance': 0},
        {'name': 'ضرائب مستحقة', 'code': '2130', 'account_type_code': '2100', 'opening_balance': 0},
        {'name': 'قروض قصيرة الأجل', 'code': '2140', 'account_type_code': '2100', 'opening_balance': 0},
        
        # الخصوم طويلة الأجل
        {'name': 'قروض طويلة الأجل', 'code': '2210', 'account_type_code': '2200', 'opening_balance': 0},
        
        # رأس المال
        {'name': 'رأس المال المدفوع', 'code': '3110', 'account_type_code': '3100', 'opening_balance': 1000000},
        {'name': 'الأرباح المحتجزة', 'code': '3210', 'account_type_code': '3200', 'opening_balance': 250000},
        
        # الإيرادات
        {'name': 'مبيعات المحروقات', 'code': '4110', 'account_type_code': '4100', 'opening_balance': 0},
        {'name': 'إيرادات الخدمات', 'code': '4120', 'account_type_code': '4100', 'opening_balance': 0},
        {'name': 'إيرادات أخرى', 'code': '4210', 'account_type_code': '4200', 'opening_balance': 0},
        
        # المصروفات
        {'name': 'رواتب وأجور', 'code': '5110', 'account_type_code': '5100', 'opening_balance': 0},
        {'name': 'إيجارات', 'code': '5120', 'account_type_code': '5100', 'opening_balance': 0},
        {'name': 'كهرباء ومياه', 'code': '5130', 'account_type_code': '5100', 'opening_balance': 0},
        {'name': 'صيانة وإصلاحات', 'code': '5140', 'account_type_code': '5100', 'opening_balance': 0},
        {'name': 'مصروفات إدارية', 'code': '5210', 'account_type_code': '5200', 'opening_balance': 0},
        {'name': 'مصروفات تسويق', 'code': '5220', 'account_type_code': '5200', 'opening_balance': 0},
        
        # تكلفة البضاعة المباعة
        {'name': 'تكلفة المحروقات المباعة', 'code': '6110', 'account_type_code': '6100', 'opening_balance': 0},
    ]
    
    print("إنشاء الحسابات الأساسية...")
    for data in accounts_data:
        account_type = AccountType.objects.get(code=data['account_type_code'])
        
        account, created = Account.objects.get_or_create(
            code=data['code'],
            defaults={
                'name': data['name'],
                'account_type': account_type,
                'opening_balance': data['opening_balance'],
                'current_balance': data['opening_balance'],
                'created_by': admin_user
            }
        )
        if created:
            print(f"  ✓ تم إنشاء الحساب: {account.name} ({account.code})")
        else:
            print(f"  - الحساب موجود: {account.name} ({account.code})")

def create_payment_methods():
    """إنشاء طرق الدفع الأساسية"""
    # الحصول على الحسابات المطلوبة
    cash_account = Account.objects.get(code='1110')  # النقدية
    bank_account = Account.objects.get(code='1120')  # البنك
    
    payment_methods_data = [
        {'name': 'نقدي', 'payment_type': 'cash', 'account': cash_account},
        {'name': 'تحويل بنكي', 'payment_type': 'bank_transfer', 'account': bank_account},
        {'name': 'شيك', 'payment_type': 'check', 'account': bank_account},
        {'name': 'بطاقة ائتمان', 'payment_type': 'credit_card', 'account': bank_account},
        {'name': 'بطاقة خصم', 'payment_type': 'debit_card', 'account': bank_account},
    ]
    
    print("إنشاء طرق الدفع...")
    for data in payment_methods_data:
        payment_method, created = PaymentMethod.objects.get_or_create(
            name=data['name'],
            defaults=data
        )
        if created:
            print(f"  ✓ تم إنشاء طريقة الدفع: {payment_method.name}")
        else:
            print(f"  - طريقة الدفع موجودة: {payment_method.name}")

def create_accounting_period():
    """إنشاء الفترة المحاسبية الحالية"""
    # الحصول على مستخدم افتراضي
    admin_user = User.objects.filter(is_superuser=True).first()
    
    current_year = datetime.now().year
    start_date = datetime(current_year, 1, 1).date()
    end_date = datetime(current_year, 12, 31).date()
    
    period, created = AccountingPeriod.objects.get_or_create(
        name=f"السنة المالية {current_year}",
        defaults={
            'start_date': start_date,
            'end_date': end_date,
            'is_current': True,
            'created_by': admin_user
        }
    )
    
    if created:
        print(f"✓ تم إنشاء الفترة المحاسبية: {period.name}")
    else:
        print(f"- الفترة المحاسبية موجودة: {period.name}")

def main():
    """تشغيل جميع وظائف إنشاء البيانات الأولية"""
    print("بدء إنشاء البيانات الأولية للنظام المالي...")
    print("=" * 50)
    
    try:
        create_account_types()
        print()
        
        create_accounts()
        print()
        
        create_payment_methods()
        print()
        
        create_accounting_period()
        print()
        
        print("=" * 50)
        print("✅ تم إنشاء جميع البيانات الأولية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات الأولية: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
