#!/bin/bash

echo "تثبيت المكتبات المطلوبة لنظام إدارة مخازن المحروقات..."

# تفعيل البيئة الافتراضية إذا كانت موجودة
if [ -d "venv" ]; then
    echo "تفعيل البيئة الافتراضية..."
    source venv/bin/activate
fi

# تحديث pip
echo "تحديث pip..."
python -m pip install --upgrade pip

# تثبيت المكتبات الأساسية
echo "تثبيت Django..."
pip install Django==4.2.7

echo "تثبيت Pillow (للصور)..."
pip install Pillow==10.1.0

echo "تثبيت ReportLab (لملفات PDF)..."
pip install reportlab==4.0.7

echo "تثبيت OpenPyXL (لملفات Excel)..."
pip install openpyxl==3.1.2

echo "تثبيت Python-decouple (للإعدادات)..."
pip install python-decouple==3.8

# تثبيت جميع المكتبات من requirements.txt
echo "تثبيت جميع المكتبات من requirements.txt..."
pip install -r requirements.txt

echo "✅ تم تثبيت جميع المكتبات بنجاح!"
echo "يمكنك الآن تشغيل الخادم باستخدام: python manage.py runserver"
