{% extends 'fuel_storage/admin/base.html' %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">إدارة المستخدمين</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users text-primary"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{% url 'fuel_storage:admin:user_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة مستخدم جديد
            </a>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني">
            </div>
            <div class="col-md-3">
                <label for="user_type" class="form-label">نوع المستخدم</label>
                <select class="form-select" id="user_type" name="user_type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in user_types %}
                        <option value="{{ value }}" {% if user_type_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i>
            قائمة المستخدمين
            <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }} مستخدم</span>
        </h5>
    </div>
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>نوع المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user_obj in page_obj %}
                            <tr>
                                <td>
                                    <strong>{{ user_obj.username }}</strong>
                                    {% if user_obj.is_superuser %}
                                        <span class="badge bg-danger ms-1">مدير عام</span>
                                    {% endif %}
                                </td>
                                <td>{{ user_obj.full_name }}</td>
                                <td>
                                    <span class="badge bg-info">{{ user_obj.get_user_type_display }}</span>
                                </td>
                                <td>{{ user_obj.email|default:"-" }}</td>
                                <td>{{ user_obj.date_joined|date:"Y-m-d" }}</td>
                                <td>
                                    {% if user_obj.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'fuel_storage:admin:user_detail' user_obj.id %}" 
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'fuel_storage:admin:user_update' user_obj.id %}" 
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if user_obj != user %}
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="toggleStatus('{% url 'fuel_storage:admin:user_toggle_status' user_obj.id %}')"
                                                    title="{% if user_obj.is_active %}إلغاء التفعيل{% else %}تفعيل{% endif %}">
                                                <i class="fas fa-{% if user_obj.is_active %}ban{% else %}check{% endif %}"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete('{% url 'fuel_storage:admin:user_delete' user_obj.id %}', '{{ user_obj.full_name }}')"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="تصفح المستخدمين">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if user_type_filter %}&user_type={{ user_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مستخدمين</h5>
                <p class="text-muted">لم يتم العثور على أي مستخدمين مطابقين لمعايير البحث.</p>
                <a href="{% url 'fuel_storage:admin:user_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- CSRF Token للعمليات AJAX -->
{% csrf_token %}
{% endblock %}
