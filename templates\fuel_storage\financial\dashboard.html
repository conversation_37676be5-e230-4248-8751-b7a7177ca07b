{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التحكم المالية{% endblock %}

{% block extra_css %}
<style>
    .financial-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .financial-card:hover {
        transform: translateY(-5px);
    }
    
    .financial-card .icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.8;
    }
    
    .financial-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .financial-card p {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .recent-transactions {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }
    
    .transaction-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }
    
    .transaction-item:last-child {
        border-bottom: none;
    }
    
    .transaction-amount.debit {
        color: #e74c3c;
    }
    
    .transaction-amount.credit {
        color: #27ae60;
    }
    
    .quick-action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 15px 25px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
    }
    
    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }
    
    .balance-positive {
        color: #27ae60;
    }
    
    .balance-negative {
        color: #e74c3c;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 mb-3">
                <i class="fas fa-chart-line me-2"></i>
                لوحة التحكم المالية
            </h1>
            <p class="text-muted">نظرة شاملة على الوضع المالي للشركة</p>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="financial-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="icon">
                    <i class="fas fa-coins"></i>
                </div>
                <h3>{{ total_assets|floatformat:0 }}</h3>
                <p>إجمالي الأصول</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="financial-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3>{{ total_liabilities|floatformat:0 }}</h3>
                <p>إجمالي الخصوم</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="financial-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <h3>{{ total_equity|floatformat:0 }}</h3>
                <p>حقوق الملكية</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="financial-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="icon">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <h3>{{ total_invoices }}</h3>
                <p>إجمالي الفواتير</p>
            </div>
        </div>
    </div>

    <!-- Monthly Performance -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="chart-container">
                <h4 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    الأداء الشهري
                </h4>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="balance-positive">{{ monthly_sales|floatformat:0 }}</h5>
                            <p class="text-muted">المبيعات هذا الشهر</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="balance-negative">{{ monthly_purchases|floatformat:0 }}</h5>
                            <p class="text-muted">المشتريات هذا الشهر</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="progress" style="height: 10px;">
                        {% if monthly_sales > 0 %}
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-3">
            <div class="chart-container">
                <h4 class="mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات مالية
                </h4>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-clock me-2"></i>
                    <strong>{{ overdue_invoices }}</strong> فاتورة متأخرة السداد
                </div>
                {% if current_period %}
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-calendar me-2"></i>
                    الفترة المحاسبية الحالية: <strong>{{ current_period.name }}</strong>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-container">
                <h4 class="mb-3">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h4>
                <div class="text-center">
                    <a href="{% url 'fuel_storage:accounts_list' %}" class="quick-action-btn">
                        <i class="fas fa-list me-2"></i>
                        عرض الحسابات
                    </a>
                    <a href="{% url 'fuel_storage:invoices_list' %}" class="quick-action-btn">
                        <i class="fas fa-file-invoice me-2"></i>
                        إدارة الفواتير
                    </a>
                    <a href="{% url 'fuel_storage:payments_list' %}" class="quick-action-btn">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        المدفوعات
                    </a>
                    <a href="{% url 'fuel_storage:balance_sheet_report' %}" class="quick-action-btn">
                        <i class="fas fa-balance-scale me-2"></i>
                        الميزانية العمومية
                    </a>
                    <a href="{% url 'fuel_storage:income_statement_report' %}" class="quick-action-btn">
                        <i class="fas fa-chart-line me-2"></i>
                        قائمة الدخل
                    </a>
                    <a href="{% url 'fuel_storage:cash_flow_report' %}" class="quick-action-btn">
                        <i class="fas fa-exchange-alt me-2"></i>
                        التدفق النقدي
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions and Invoices -->
    <div class="row">
        <div class="col-lg-6 mb-3">
            <div class="recent-transactions">
                <h4 class="mb-3">
                    <i class="fas fa-history me-2"></i>
                    أحدث المعاملات
                </h4>
                {% for transaction in recent_transactions %}
                <div class="transaction-item">
                    <div>
                        <strong>{{ transaction.account.name }}</strong>
                        <br>
                        <small class="text-muted">{{ transaction.description }}</small>
                    </div>
                    <div class="text-end">
                        {% if transaction.debit_amount > 0 %}
                            <span class="transaction-amount debit">
                                -{{ transaction.debit_amount|floatformat:2 }}
                            </span>
                        {% else %}
                            <span class="transaction-amount credit">
                                +{{ transaction.credit_amount|floatformat:2 }}
                            </span>
                        {% endif %}
                        <br>
                        <small class="text-muted">{{ transaction.transaction_date }}</small>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد معاملات حديثة</p>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-lg-6 mb-3">
            <div class="recent-transactions">
                <h4 class="mb-3">
                    <i class="fas fa-file-invoice me-2"></i>
                    أحدث الفواتير
                </h4>
                {% for invoice in recent_invoices %}
                <div class="transaction-item">
                    <div>
                        <strong>{{ invoice.invoice_number }}</strong>
                        <br>
                        <small class="text-muted">
                            {% if invoice.supplier %}
                                {{ invoice.supplier.full_name }}
                            {% elif invoice.beneficiary %}
                                {{ invoice.beneficiary.full_name }}
                            {% endif %}
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="transaction-amount {% if invoice.invoice_type == 'sales' %}credit{% else %}debit{% endif %}">
                            {{ invoice.total_amount|floatformat:2 }}
                        </span>
                        <br>
                        <small class="badge bg-{% if invoice.status == 'paid' %}success{% elif invoice.status == 'overdue' %}danger{% else %}warning{% endif %}">
                            {{ invoice.get_status_display }}
                        </small>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">لا توجد فواتير حديثة</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير hover للبطاقات
        const cards = document.querySelectorAll('.financial-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث الوقت كل دقيقة
        setInterval(function() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.title = `لوحة التحكم المالية - ${timeString}`;
        }, 60000);
    });
</script>
{% endblock %}
