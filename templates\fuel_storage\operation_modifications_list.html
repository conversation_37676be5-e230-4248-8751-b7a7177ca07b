{% extends 'base.html' %}

{% block title %}تعديلات العمليات - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">تعديلات العمليات</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/operationmodification/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة تعديل جديد
        </a>
    </div>
</div>

{% if modifications %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>المخزن</th>
                    <th>نوع العملية</th>
                    <th>الصنف</th>
                    <th>الكمية السابقة</th>
                    <th>الكمية الجديدة</th>
                    <th>الفرق</th>
                    <th>عُدل بواسطة</th>
                    <th>تاريخ التعديل</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for modification in modifications %}
                <tr>
                    <td>{{ modification.storage.name }}</td>
                    <td>
                        <span class="badge {% if modification.operation_type == 'incoming' %}bg-success{% else %}bg-danger{% endif %}">
                            {{ modification.get_operation_type_display }}
                        </span>
                    </td>
                    <td>{{ modification.category.name }}</td>
                    <td>{{ modification.previous_quantity }}</td>
                    <td>{{ modification.new_quantity }}</td>
                    <td>
                        {% with diff=modification.new_quantity|floatformat:3|add:"-"|add:modification.previous_quantity|floatformat:3 %}
                            <span class="{% if diff > 0 %}text-success{% elif diff < 0 %}text-danger{% else %}text-muted{% endif %}">
                                {{ diff|floatformat:3 }}
                            </span>
                        {% endwith %}
                    </td>
                    <td>{{ modification.modified_by.full_name|default:modification.modified_by.username }}</td>
                    <td>{{ modification.modification_date|date:"Y-m-d H:i" }}</td>
                    <td>
                        <a href="/admin/fuel_storage/operationmodification/{{ modification.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد تعديلات على العمليات حتى الآن.
    </div>
{% endif %}
{% endblock %}
