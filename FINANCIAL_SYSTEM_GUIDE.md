# 📊 دليل استخدام النظام المالي

## نظرة عامة
تم إضافة نظام إدارة مالية ومحاسبة شامل إلى نظام إدارة مخازن المحروقات. يوفر النظام إدارة كاملة للحسابات، الفواتير، المدفوعات، والتقارير المالية.

## 🚀 الوصول للنظام المالي

### الروابط الرئيسية:
- **لوحة التحكم المالية**: http://127.0.0.1:8000/financial/
- **إدارة الحسابات**: http://127.0.0.1:8000/financial/accounts/
- **إدارة الفواتير**: http://127.0.0.1:8000/financial/invoices/
- **إدارة المدفوعات**: http://127.0.0.1:8000/financial/payments/

### التقارير المالية:
- **الميزانية العمومية**: http://127.0.0.1:8000/financial/reports/balance-sheet/
- **قائمة الدخل**: http://127.0.0.1:8000/financial/reports/income-statement/
- **التدفق النقدي**: http://127.0.0.1:8000/financial/reports/cash-flow/

## 💰 إدارة المدفوعات

### إضافة دفعة جديدة:
1. انتقل إلى قائمة المدفوعات: http://127.0.0.1:8000/financial/payments/
2. اضغط على زر "إضافة دفعة جديدة"
3. اختر نوع الدفعة:
   - **مقبوض**: أموال مستلمة من العملاء
   - **مدفوع**: أموال مدفوعة للموردين
4. املأ البيانات المطلوبة:
   - المبلغ (مطلوب)
   - تاريخ الدفع (مطلوب)
   - طريقة الدفع (مطلوب)
   - رقم المرجع (اختياري)
5. ربط الدفعة (اختياري):
   - بفاتورة موجودة
   - بمورد مباشرة
   - بمستفيد مباشرة
6. إضافة ملاحظات (اختياري)
7. اضغط "حفظ الدفعة"

### عرض تفاصيل الدفعة:
- في قائمة المدفوعات، اضغط على زر العين (👁) لعرض التفاصيل
- ستظهر جميع معلومات الدفعة مع إمكانية الطباعة

## 📋 إدارة الفواتير

### الفواتير التلقائية:
- يتم إنشاء فواتير المشتريات تلقائياً عند إنشاء عملية وارد
- يتم إنشاء فواتير المبيعات تلقائياً عند إنشاء عملية صادر
- يتم ربط الفواتير بالقيود المحاسبية تلقائياً

### أنواع الفواتير:
- **فاتورة مبيعات**: للعمليات الصادرة
- **فاتورة مشتريات**: للعمليات الواردة
- **فاتورة مرتجع مبيعات**: لمرتجعات الصادر
- **فاتورة مرتجع مشتريات**: لمرتجعات الوارد

### حالات الفواتير:
- **مسودة**: فاتورة غير مكتملة
- **مرسلة**: فاتورة جاهزة للدفع
- **مدفوعة**: تم دفع المبلغ كاملاً
- **مدفوعة جزئياً**: تم دفع جزء من المبلغ
- **متأخرة**: تجاوزت تاريخ الاستحقاق
- **ملغية**: فاتورة ملغاة

## 🏦 إدارة الحسابات

### أنواع الحسابات:
- **الأصول**: النقدية، البنوك، المخزون، العملاء
- **الخصوم**: الموردين، القروض، المصروفات المستحقة
- **حقوق الملكية**: رأس المال، الأرباح المحتجزة
- **الإيرادات**: مبيعات المحروقات، إيرادات أخرى
- **المصروفات**: الرواتب، الإيجارات، الكهرباء
- **تكلفة البضاعة المباعة**: تكلفة المحروقات المباعة

### الحسابات الأساسية المُنشأة:
- **1110**: النقدية (50,000 ريال)
- **1120**: البنك - الحساب الجاري (100,000 ريال)
- **1130**: العملاء
- **1140**: المخزون (200,000 ريال)
- **2110**: الموردين
- **3110**: رأس المال المدفوع (500,000 ريال)
- **4110**: مبيعات المحروقات
- **5110**: مصروفات تشغيلية
- **6110**: تكلفة المحروقات المباعة

## 📊 التقارير المالية

### 1. الميزانية العمومية:
- عرض الأصول والخصوم وحقوق الملكية
- التحقق من توازن الميزانية
- إمكانية اختيار تاريخ محدد
- طباعة التقرير

### 2. قائمة الدخل:
- عرض الإيرادات والمصروفات
- حساب إجمالي الربح وصافي الربح
- اختيار فترة زمنية محددة
- مؤشرات بصرية للأداء

### 3. تقرير التدفق النقدي:
- التدفقات التشغيلية
- التدفقات الاستثمارية
- التدفقات التمويلية
- صافي التدفق النقدي
- رسوم بيانية تفاعلية

## 🔄 الربط التلقائي

### العمليات ← الفواتير:
- عملية وارد → فاتورة مشتريات
- عملية صادر → فاتورة مبيعات
- مرتجع وارد → فاتورة مرتجع مشتريات
- مرتجع صادر → فاتورة مرتجع مبيعات

### الفواتير ← القيود المحاسبية:
- فاتورة مشتريات → مدين: المخزون، دائن: الموردين
- فاتورة مبيعات → مدين: العملاء، دائن: المبيعات
- تكلفة البضاعة المباعة → مدين: التكلفة، دائن: المخزون

### المدفوعات ← القيود المحاسبية:
- دفعة مقبوضة → مدين: النقدية/البنك، دائن: العملاء
- دفعة مدفوعة → مدين: الموردين، دائن: النقدية/البنك

## 🛠️ طرق الدفع المتاحة:
- **نقدي**: للمدفوعات النقدية
- **تحويل بنكي**: للتحويلات البنكية
- **شيك**: للدفع بالشيكات
- **بطاقة ائتمان**: للدفع بالبطاقات الائتمانية
- **بطاقة خصم**: للدفع بالبطاقات المصرفية

## 📅 الفترات المحاسبية:
- تم إنشاء فترة محاسبية للسنة الحالية
- إمكانية إنشاء فترات جديدة
- إغلاق الفترات وإنشاء قيود الإقفال

## 🔐 الأمان والصلاحيات:
- جميع العمليات مرتبطة بالمستخدم المنشئ
- تتبع تواريخ الإنشاء والتعديل
- حماية من التلاعب في القيود المرحلة
- نظام صلاحيات متقدم

## 💡 نصائح للاستخدام:
1. تأكد من إنشاء الحسابات الأساسية قبل البدء
2. راجع الفواتير التلقائية قبل ترحيلها
3. استخدم أرقام المرجع لتسهيل التتبع
4. راجع التقارير المالية بانتظام
5. احتفظ بنسخ احتياطية من البيانات المالية

## 🆘 الدعم:
في حالة وجود مشاكل أو استفسارات، يمكن مراجعة:
- لوحة الإدارة: http://127.0.0.1:8000/admin/
- سجلات النظام في قسم SystemLog
- التحقق من صحة البيانات في Django shell
