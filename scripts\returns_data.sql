-- إدراج بيانات تجريبية للمرتجعات

-- أولاً، نحتاج لإدراج بعض عمليات الوارد والصادر للاختبار
-- (هذا مثال فقط - يجب أن تكون هناك عمليات حقيقية في النظام)

-- إدراج مستخدم تجريبي إذا لم يكن موجوداً
INSERT OR IGNORE INTO fuel_storage_customuser (
    username, password, is_superuser, first_name, last_name, 
    email, is_staff, is_active, date_joined, full_name, user_type
) VALUES (
    'test_user', 'pbkdf2_sha256$260000$test', 0, 'مستخدم', 'تجريبي',
    '<EMAIL>', 0, 1, datetime('now'), 'مستخدم تجريبي', 'operator'
);

-- ملاحظة: في الاستخدام الفعلي، ستحتاج لإنشاء عمليات وارد وصادر أولاً
-- من خلال واجهة الإدارة قبل إنشاء المرتجعات
