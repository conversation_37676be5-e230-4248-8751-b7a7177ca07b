{% extends 'base.html' %}

{% block title %}{{ title }} - نظام إدارة مخازن المحروقات{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        border-radius: 0 0 20px 20px;
    }

    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }

    .form-section {
        border-bottom: 1px solid #f1f3f4;
        padding: 2rem;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #e74c3c;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #f1f3f4;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #e74c3c;
        box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
    }

    .btn-save {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
        color: white;
    }

    .btn-cancel {
        border: 2px solid #95a5a6;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: #95a5a6;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #95a5a6;
        color: white;
    }

    .required-field::after {
        content: " *";
        color: #e74c3c;
        font-weight: bold;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    .error-message {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="form-header text-center">
    <div class="container">
        <h1 style="margin: 0; font-weight: 700; display: flex; align-items: center; justify-content: center; gap: 1rem;">
            <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-{% if user_obj %}edit{% else %}plus{% endif %}"></i>
            </div>
            {{ title }}
        </h1>
        <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">
            {% if user_obj %}تعديل بيانات المستخدم في النظام{% else %}إضافة مستخدم جديد للنظام{% endif %}
        </p>
    </div>
</div>

<!-- أزرار التحكم -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <a href="{% url 'fuel_storage:manage_users' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i>
            العودة لقائمة المستخدمين
        </a>
    </div>
    {% if user_obj %}
        <div>
            <span class="badge bg-info">آخر تسجيل دخول: {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول بعد" }}</span>
        </div>
    {% endif %}
</div>

<!-- نموذج المستخدم -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="form-card">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <!-- معلومات أساسية -->
                <div class="form-section">
                    <h4 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        المعلومات الأساسية
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label required-field">
                                {{ form.username.label }}
                            </label>
                            {{ form.username }}
                            {% if form.username.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.username.help_text %}
                                <div class="form-text">{{ form.username.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.full_name.id_for_label }}" class="form-label required-field">
                                {{ form.full_name.label }}
                            </label>
                            {{ form.full_name }}
                            {% if form.full_name.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.full_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                {{ form.email.label }}
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.user_type.id_for_label }}" class="form-label required-field">
                                {{ form.user_type.label }}
                            </label>
                            {{ form.user_type }}
                            {% if form.user_type.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.user_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="form-section">
                    <h4 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        معلومات الاتصال
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                {{ form.phone_number.label }}
                            </label>
                            {{ form.phone_number }}
                            {% if form.phone_number.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.phone_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.department.id_for_label }}" class="form-label">
                                {{ form.department.label }}
                            </label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.department.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- كلمة المرور (للمستخدمين الجدد فقط) -->
                {% if form.password1 %}
                <div class="form-section">
                    <h4 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        كلمة المرور
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label required-field">
                                {{ form.password1.label }}
                            </label>
                            {{ form.password1 }}
                            {% if form.password1.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.password1.help_text %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label required-field">
                                {{ form.password2.label }}
                            </label>
                            {{ form.password2 }}
                            {% if form.password2.errors %}
                                <div class="error-message">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- حالة المستخدم (للتعديل فقط) -->
                {% if form.is_active %}
                <div class="form-section">
                    <h4 class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-toggle-on"></i>
                        </div>
                        حالة المستخدم
                    </h4>
                    
                    <div class="form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            {{ form.is_active.label }}
                        </label>
                        {% if form.is_active.errors %}
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                {% for error in form.is_active.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- أزرار الحفظ -->
                <div class="form-section">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save"></i>
                                {% if user_obj %}تحديث البيانات{% else %}إنشاء المستخدم{% endif %}
                            </button>
                            <a href="{% url 'fuel_storage:manage_users' %}" class="btn btn-cancel ms-3">
                                <i class="fas fa-times"></i>
                                إلغاء
                            </a>
                        </div>
                        {% if user_obj %}
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i>
                                    تم الإنشاء: {{ user_obj.date_joined|date:"Y-m-d H:i" }}
                                </small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>

        <!-- معلومات إضافية للمستخدم الموجود -->
        {% if user_obj %}
        <div class="user-info-card">
            <h6 style="color: #495057; font-weight: 700; margin-bottom: 1rem;">
                <i class="fas fa-info-circle"></i>
                معلومات إضافية
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>الحالة:</strong> 
                        {% if user_obj.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </p>
                    <p><strong>مدير عام:</strong> 
                        {% if user_obj.is_superuser %}
                            <span class="badge bg-danger">نعم</span>
                        {% else %}
                            <span class="badge bg-secondary">لا</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>تاريخ الإنشاء:</strong> {{ user_obj.date_joined|date:"Y-m-d H:i" }}</p>
                    <p><strong>آخر تسجيل دخول:</strong> {{ user_obj.last_login|date:"Y-m-d H:i"|default:"لم يسجل دخول بعد" }}</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
