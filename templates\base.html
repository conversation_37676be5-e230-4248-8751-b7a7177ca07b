{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة مخازن المحروقات{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{% static 'css/professional-theme.css' %}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --sidebar-bg: #2c3e50;
            --sidebar-hover: #34495e;
            --text-light: #ecf0f1;
            --text-muted: #bdc3c7;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            border: none;
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .sidebar {
            min-height: calc(100vh - 76px);
            background: linear-gradient(180deg, var(--sidebar-bg) 0%, #34495e 100%);
            box-shadow: 2px 0 20px rgba(0,0,0,0.1);
            border-radius: 0 15px 15px 0;
        }

        .sidebar .nav-link {
            color: var(--text-muted);
            padding: 0.75rem 1.5rem;
            margin: 0.2rem 0.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link:hover {
            color: var(--text-light);
            background: var(--sidebar-hover);
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-left: 0.5rem;
            font-size: 1.1rem;
        }

        .sidebar-heading {
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            font-weight: 600;
            color: var(--text-muted);
            padding: 1rem 1.5rem 0.5rem;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .sidebar-heading::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 1.5rem;
            right: 1.5rem;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--text-muted), transparent);
        }

        .financial-section {
            border-top: 1px solid #34495e;
            margin-top: 1rem;
            padding-top: 1rem;
        }

        .card {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: white;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.25);
        }

        .card:hover::before {
            opacity: 1;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 1rem;
            padding: 2rem;
            min-height: calc(100vh - 120px);
        }

        .btn {
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            padding: 0.75rem 2rem;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
        }

        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 5px 15px rgba(67, 233, 123, 0.4);
        }

        .btn-success:hover {
            box-shadow: 0 15px 35px rgba(67, 233, 123, 0.6);
        }

        .btn-danger {
            background: var(--danger-gradient);
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
        }

        .btn-danger:hover {
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.6);
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }

        .table thead th {
            background: var(--primary-gradient);
            border: none;
            font-weight: 700;
            color: white;
            padding: 1.25rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
            border: none;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(5px) scale(1.01);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .table tbody td {
            padding: 1.25rem;
            border-top: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        /* تأثيرات خاصة */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* تأثيرات التمرير */
        .scroll-shadow {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: box-shadow 0.3s ease;
        }

        /* شارات الحالة */
        .badge {
            border-radius: 20px;
            padding: 0.5rem 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: var(--success-gradient);
            color: white;
        }

        .badge-danger {
            background: var(--danger-gradient);
            color: white;
        }

        .badge-warning {
            background: var(--warning-gradient);
            color: #8b4513;
        }

        @media (max-width: 768px) {
            .sidebar {
                border-radius: 0;
            }

            .main-content {
                margin: 0.5rem;
                padding: 1rem;
                border-radius: 15px;
            }

            .btn {
                padding: 0.5rem 1.5rem;
                font-size: 0.8rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-3px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'fuel_storage:dashboard' %}">
                <i class="fas fa-gas-pump me-2"></i>
                نظام إدارة مخازن المحروقات
            </a>
            <div class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                    <span class="navbar-text me-3">
                        مرحباً، {{ user.full_name|default:user.username }}
                    </span>
                    <a class="nav-link" href="/admin/">لوحة الإدارة</a>
                    <a class="nav-link" href="{% url 'admin:logout' %}">تسجيل الخروج</a>
                {% else %}
                    <a class="nav-link" href="{% url 'admin:login' %}">تسجيل الدخول</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid p-0">
        <div class="row g-0">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-4">
                    <div class="sidebar-heading">القائمة الرئيسية</div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_list' %}">
                                <i class="fas fa-warehouse me-2"></i>
                                المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:category_list' %}">
                                <i class="fas fa-tags me-2"></i>
                                الأصناف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:supplier_list' %}">
                                <i class="fas fa-truck me-2"></i>
                                الموردون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:incoming_operations_list' %}">
                                <i class="fas fa-arrow-down me-2"></i>
                                عمليات الوارد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_items_list' %}">
                                <i class="fas fa-boxes me-2"></i>
                                أصناف المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:beneficiary_list' %}">
                                <i class="fas fa-users me-2"></i>
                                المستفيدون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:outgoing_operations_list' %}">
                                <i class="fas fa-arrow-up me-2"></i>
                                عمليات الصادر
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:operation_modifications_list' %}">
                                <i class="fas fa-edit me-2"></i>
                                تعديلات العمليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_report' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقارير المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:incoming_returns_list' %}">
                                <i class="fas fa-undo me-2"></i>
                                مرتجعات الوارد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:outgoing_returns_list' %}">
                                <i class="fas fa-redo me-2"></i>
                                مرتجعات الصادر
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:returns_summary' %}">
                                <i class="fas fa-clipboard-list me-2"></i>
                                ملخص المرتجعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:damage_operations_list' %}">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                عمليات التلف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_transfers_list' %}">
                                <i class="fas fa-exchange-alt me-2"></i>
                                التحويل المخزني
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:operations_summary' %}">
                                <i class="fas fa-chart-pie me-2"></i>
                                ملخص العمليات
                            </a>
                        </li>

                        <!-- قسم النظام المالي -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>النظام المالي</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:financial_dashboard' %}">
                                <i class="fas fa-chart-line me-2"></i>
                                لوحة التحكم المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:accounts_list' %}">
                                <i class="fas fa-list me-2"></i>
                                الحسابات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:invoices_list' %}">
                                <i class="fas fa-file-invoice me-2"></i>
                                الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:payments_list' %}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المدفوعات
                            </a>
                        </li>

                        <!-- التقارير المالية -->
                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>التقارير المالية</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:balance_sheet_report' %}">
                                <i class="fas fa-balance-scale me-2"></i>
                                الميزانية العمومية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:income_statement_report' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                قائمة الدخل
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:cash_flow_report' %}">
                                <i class="fas fa-exchange-alt me-2"></i>
                                التدفق النقدي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:reports_dashboard' %}">
                                <i class="fas fa-file-alt me-2"></i>
                                التقارير المتقدمة
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 p-0">
                <div class="main-content">
                    {% block content %}
                    {% endblock %}
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تأثيرات التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثير fade-in للعناصر
            const elements = document.querySelectorAll('.card, .table, .btn');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // تأثير التمرير للـ navbar
            let lastScrollTop = 0;
            const navbar = document.querySelector('.navbar');

            window.addEventListener('scroll', function() {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop) {
                    // التمرير لأسفل
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // التمرير لأعلى
                    navbar.style.transform = 'translateY(0)';
                }

                // إضافة ظل عند التمرير
                if (scrollTop > 50) {
                    navbar.classList.add('scroll-shadow');
                } else {
                    navbar.classList.remove('scroll-shadow');
                }

                lastScrollTop = scrollTop;
            });

            // تأثيرات hover للبطاقات
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.boxShadow = '0 25px 50px rgba(102, 126, 234, 0.3)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.1)';
                });
            });

            // تأثيرات الأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // تأثير الموجة
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });

        // CSS للتأثيرات الإضافية
        const style = document.createElement('style');
        style.textContent = `
            .navbar {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple-animation 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .card {
                will-change: transform, box-shadow;
            }

            .btn {
                position: relative;
                overflow: hidden;
                will-change: transform;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
