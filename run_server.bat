@echo off
echo ========================================
echo   نظام إدارة مخازن المحروقات
echo   Fuel Storage Management System
echo ========================================
echo.

echo تفعيل البيئة الافتراضية...
echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo فحص النظام...
echo Checking system...
python manage.py check

if %errorlevel% neq 0 (
    echo.
    echo خطأ في فحص النظام!
    echo System check failed!
    pause
    exit /b 1
)

echo.
echo تطبيق التحديثات على قاعدة البيانات...
echo Applying database migrations...
python manage.py migrate

echo.
echo تشغيل الخادم...
echo Starting server...
echo.
echo يمكنك الوصول للنظام عبر:
echo You can access the system at:
echo.
echo النظام الرئيسي: http://localhost:8000/
echo Main System: http://localhost:8000/
echo.
echo لوحة التحكم الإدارية: http://localhost:8000/admin/
echo Admin Dashboard: http://localhost:8000/admin/
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver 8000

echo.
echo تم إيقاف الخادم
echo Server stopped
pause
