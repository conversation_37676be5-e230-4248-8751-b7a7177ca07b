# Generated by Django 4.2.7 on 2025-08-05 00:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fuel_storage', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الحساب')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('opening_balance', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'حساب',
                'verbose_name_plural': 'الحسابات',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='AccountingPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفترة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('is_closed', models.BooleanField(default=False, verbose_name='مغلقة')),
                ('is_current', models.BooleanField(default=False, verbose_name='الفترة الحالية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإغلاق')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'فترة محاسبية',
                'verbose_name_plural': 'الفترات المحاسبية',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الحساب')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز النوع')),
                ('account_type', models.CharField(choices=[('asset', 'أصول'), ('liability', 'خصوم'), ('equity', 'حقوق الملكية'), ('revenue', 'إيرادات'), ('expense', 'مصروفات'), ('cost_of_goods', 'تكلفة البضاعة المباعة')], max_length=20, verbose_name='نوع الحساب')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'نوع حساب',
                'verbose_name_plural': 'أنواع الحسابات',
            },
        ),
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الميزانية')),
                ('fiscal_year', models.IntegerField(verbose_name='السنة المالية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('total_budget', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='إجمالي الميزانية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'ميزانية',
                'verbose_name_plural': 'الميزانيات',
                'ordering': ['-fiscal_year'],
            },
        ),
        migrations.CreateModel(
            name='BudgetItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('budgeted_amount', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='المبلغ المخطط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.account', verbose_name='الحساب')),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.budget', verbose_name='الميزانية')),
            ],
            options={
                'verbose_name': 'بند ميزانية',
                'verbose_name_plural': 'بنود الميزانيات',
                'unique_together': {('budget', 'account')},
            },
        ),
        migrations.CreateModel(
            name='CashFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('flow_type', models.CharField(choices=[('inflow', 'تدفق داخل'), ('outflow', 'تدفق خارج')], max_length=20, verbose_name='نوع التدفق')),
                ('category', models.CharField(choices=[('operating', 'أنشطة تشغيلية'), ('investing', 'أنشطة استثمارية'), ('financing', 'أنشطة تمويلية')], max_length=20, verbose_name='الفئة')),
                ('amount', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.account', verbose_name='الحساب')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تدفق نقدي',
                'verbose_name_plural': 'التدفقات النقدية',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_type', models.CharField(choices=[('sales', 'فاتورة مبيعات'), ('purchase', 'فاتورة مشتريات'), ('return_sales', 'فاتورة مرتجع مبيعات'), ('return_purchase', 'فاتورة مرتجع مشتريات')], max_length=20, verbose_name='نوع الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('partially_paid', 'مدفوعة جزئياً'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('subtotal', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='المجموع الفرعي')),
                ('tax_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('discount_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('total_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='المبلغ المدفوع')),
                ('remaining_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='المبلغ المتبقي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('terms_and_conditions', models.TextField(blank=True, null=True, verbose_name='الشروط والأحكام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('beneficiary', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.beneficiary', verbose_name='المستفيد')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('incoming_operation', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
                ('incoming_return', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
                ('outgoing_operation', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
                ('outgoing_return', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'فاتورة',
                'verbose_name_plural': 'الفواتير',
                'ordering': ['-invoice_date', '-invoice_number'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم %')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15.0, max_digits=5, verbose_name='معدل الضريبة %')),
                ('line_total', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='إجمالي السطر')),
                ('discount_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.invoice', verbose_name='الفاتورة')),
            ],
            options={
                'verbose_name': 'بند فاتورة',
                'verbose_name_plural': 'بنود الفواتير',
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')),
                ('entry_date', models.DateField(verbose_name='تاريخ القيد')),
                ('entry_type', models.CharField(choices=[('manual', 'يدوي'), ('automatic', 'تلقائي'), ('adjustment', 'تسوية'), ('closing', 'إقفال')], default='manual', max_length=20, verbose_name='نوع القيد')),
                ('description', models.TextField(verbose_name='البيان')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المرجع')),
                ('total_amount', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('is_posted', models.BooleanField(default=False, verbose_name='مرحل')),
                ('is_reversed', models.BooleanField(default=False, verbose_name='معكوس')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الترحيل')),
                ('accounting_period', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.accountingperiod', verbose_name='الفترة المحاسبية')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('incoming_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
                ('incoming_return', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
                ('outgoing_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
                ('outgoing_return', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='posted_entries', to=settings.AUTH_USER_MODEL, verbose_name='رحل بواسطة')),
            ],
            options={
                'verbose_name': 'قيد يومية',
                'verbose_name_plural': 'قيود اليومية',
                'ordering': ['-entry_date', '-entry_number'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الدفعة')),
                ('payment_type', models.CharField(choices=[('received', 'مقبوض'), ('paid', 'مدفوع')], max_length=20, verbose_name='نوع الدفعة')),
                ('payment_date', models.DateField(verbose_name='تاريخ الدفع')),
                ('amount', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='المبلغ')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('beneficiary', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.beneficiary', verbose_name='المستفيد')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.invoice', verbose_name='الفاتورة')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم طريقة الدفع')),
                ('payment_type', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان'), ('debit_card', 'بطاقة خصم'), ('online', 'دفع إلكتروني')], max_length=20, verbose_name='نوع الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.account', verbose_name='الحساب المرتبط')),
            ],
            options={
                'verbose_name': 'طريقة دفع',
                'verbose_name_plural': 'طرق الدفع',
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(verbose_name='البيان')),
                ('debit_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='مدين')),
                ('credit_amount', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='دائن')),
                ('transaction_date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.account', verbose_name='الحساب')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='fuel_storage.journalentry', verbose_name='قيد اليومية')),
            ],
            options={
                'verbose_name': 'معاملة محاسبية',
                'verbose_name_plural': 'المعاملات المحاسبية',
            },
        ),
        migrations.RemoveField(
            model_name='incomingoperationfile',
            name='incoming_operation',
        ),
        migrations.RemoveField(
            model_name='incomingreturnfile',
            name='incoming_return',
        ),
        migrations.RemoveField(
            model_name='outgoingoperationfile',
            name='outgoing_operation',
        ),
        migrations.RemoveField(
            model_name='outgoingreturnfile',
            name='outgoing_return',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='approved_at',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='damage_cause',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='estimated_loss',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='insurance_claim',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='investigation_report',
        ),
        migrations.RemoveField(
            model_name='damageoperation',
            name='is_approved',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='batch_number',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='expiry_date',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='salvage_value',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='severity',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='total_loss',
        ),
        migrations.RemoveField(
            model_name='damageoperationitem',
            name='unit_cost',
        ),
        migrations.RemoveField(
            model_name='operationmodification',
            name='new_price',
        ),
        migrations.RemoveField(
            model_name='operationmodification',
            name='previous_price',
        ),
        migrations.RemoveField(
            model_name='operationmodification',
            name='reason',
        ),
        migrations.AlterField(
            model_name='damageoperationitem',
            name='reason',
            field=models.CharField(choices=[('expired', 'منتهي الصلاحية'), ('contaminated', 'ملوث'), ('leaked', 'تسرب'), ('fire', 'حريق'), ('accident', 'حادث'), ('quality_issue', 'مشكلة في الجودة'), ('other', 'أخرى')], max_length=50, verbose_name='السبب'),
        ),
        migrations.AlterField(
            model_name='operationmodification',
            name='modified_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='عُدل بواسطة'),
        ),
        migrations.AlterField(
            model_name='stockalert',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='حل بواسطة'),
        ),
        migrations.DeleteModel(
            name='DamageOperationFile',
        ),
        migrations.DeleteModel(
            name='IncomingOperationFile',
        ),
        migrations.DeleteModel(
            name='IncomingReturnFile',
        ),
        migrations.DeleteModel(
            name='OutgoingOperationFile',
        ),
        migrations.DeleteModel(
            name='OutgoingReturnFile',
        ),
        migrations.AddField(
            model_name='payment',
            name='payment_method',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.paymentmethod', verbose_name='طريقة الدفع'),
        ),
        migrations.AddField(
            model_name='payment',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='cashflow',
            name='journal_entry',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.journalentry', verbose_name='قيد اليومية'),
        ),
        migrations.AddField(
            model_name='cashflow',
            name='payment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.payment', verbose_name='الدفعة'),
        ),
        migrations.AddField(
            model_name='account',
            name='account_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.accounttype', verbose_name='نوع الحساب'),
        ),
        migrations.AddField(
            model_name='account',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة'),
        ),
        migrations.AddField(
            model_name='account',
            name='parent_account',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.account', verbose_name='الحساب الأب'),
        ),
    ]
