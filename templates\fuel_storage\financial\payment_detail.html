{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الدفعة - {{ payment.payment_number }}{% endblock %}

{% block extra_css %}
<style>
    .payment-detail-container {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .payment-header {
        text-align: center;
        border-bottom: 3px solid #667eea;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .payment-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .payment-type-badge {
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .type-received {
        background: linear-gradient(135deg, #d5f4e6 0%, #a8e6cf 100%);
        color: #27ae60;
    }
    
    .type-paid {
        background: linear-gradient(135deg, #fadbd8 0%, #f5b7b1 100%);
        color: #e74c3c;
    }
    
    .payment-amount {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        margin: 30px 0;
    }
    
    .amount-received {
        color: #27ae60;
    }
    
    .amount-paid {
        color: #e74c3c;
    }
    
    .detail-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .section-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-left: 10px;
        color: #667eea;
    }
    
    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .detail-item {
        background: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .detail-label {
        font-size: 0.9rem;
        color: #7f8c8d;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .detail-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn-action {
        padding: 12px 25px;
        border-radius: 10px;
        border: none;
        font-weight: bold;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .btn-print {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-print:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
    }
    
    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        color: white;
    }
    
    .btn-back:hover {
        background: #5a6268;
        color: white;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }
    
    .status-active {
        background: #27ae60;
    }
    
    .status-inactive {
        background: #e74c3c;
    }
    
    .reference-link {
        color: #667eea;
        text-decoration: none;
        font-weight: bold;
    }
    
    .reference-link:hover {
        color: #764ba2;
        text-decoration: underline;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .payment-detail-container {
            box-shadow: none;
            border: 1px solid #ddd;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        تفاصيل الدفعة
                    </h1>
                    <p class="text-muted">عرض تفاصيل الدفعة {{ payment.payment_number }}</p>
                </div>
                <div>
                    <a href="{% url 'fuel_storage:payments_list' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة لقائمة المدفوعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Details -->
    <div class="payment-detail-container">
        <div class="payment-header">
            <div class="payment-number">{{ payment.payment_number }}</div>
            <span class="payment-type-badge type-{{ payment.payment_type }}">
                {{ payment.get_payment_type_display }}
            </span>
        </div>

        <!-- Amount -->
        <div class="payment-amount amount-{{ payment.payment_type }}">
            {% if payment.payment_type == 'received' %}+{% else %}-{% endif %}{{ payment.amount|floatformat:2 }} ريال
        </div>

        <!-- Basic Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">تاريخ الدفع</div>
                    <div class="detail-value">{{ payment.payment_date|date:"j F Y" }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">طريقة الدفع</div>
                    <div class="detail-value">
                        {{ payment.payment_method.name }}
                        <span class="status-indicator status-active"></span>
                    </div>
                </div>
                
                {% if payment.reference_number %}
                <div class="detail-item">
                    <div class="detail-label">رقم المرجع</div>
                    <div class="detail-value">{{ payment.reference_number }}</div>
                </div>
                {% endif %}
                
                <div class="detail-item">
                    <div class="detail-label">تاريخ الإنشاء</div>
                    <div class="detail-value">{{ payment.created_at|date:"j F Y - H:i" }}</div>
                </div>
            </div>
        </div>

        <!-- Reference Information -->
        {% if payment.invoice or payment.supplier or payment.beneficiary %}
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-link"></i>
                معلومات المرجع
            </div>
            
            <div class="detail-grid">
                {% if payment.invoice %}
                <div class="detail-item">
                    <div class="detail-label">الفاتورة المرتبطة</div>
                    <div class="detail-value">
                        <a href="#" class="reference-link">{{ payment.invoice.invoice_number }}</a>
                    </div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">مبلغ الفاتورة</div>
                    <div class="detail-value">{{ payment.invoice.total_amount|floatformat:2 }} ريال</div>
                </div>
                {% endif %}
                
                {% if payment.supplier %}
                <div class="detail-item">
                    <div class="detail-label">المورد</div>
                    <div class="detail-value">{{ payment.supplier.full_name }}</div>
                </div>
                
                {% if payment.supplier.phone %}
                <div class="detail-item">
                    <div class="detail-label">هاتف المورد</div>
                    <div class="detail-value">{{ payment.supplier.phone }}</div>
                </div>
                {% endif %}
                {% endif %}
                
                {% if payment.beneficiary %}
                <div class="detail-item">
                    <div class="detail-label">المستفيد</div>
                    <div class="detail-value">{{ payment.beneficiary.full_name }}</div>
                </div>
                
                {% if payment.beneficiary.phone %}
                <div class="detail-item">
                    <div class="detail-label">هاتف المستفيد</div>
                    <div class="detail-value">{{ payment.beneficiary.phone }}</div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Additional Information -->
        <div class="detail-section">
            <div class="section-title">
                <i class="fas fa-user-cog"></i>
                معلومات إضافية
            </div>
            
            <div class="detail-grid">
                <div class="detail-item">
                    <div class="detail-label">أنشئ بواسطة</div>
                    <div class="detail-value">{{ payment.created_by.full_name }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">نوع المستخدم</div>
                    <div class="detail-value">{{ payment.created_by.get_user_type_display }}</div>
                </div>
                
                {% if payment.notes %}
                <div class="detail-item" style="grid-column: 1 / -1;">
                    <div class="detail-label">ملاحظات</div>
                    <div class="detail-value">{{ payment.notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons no-print">
            <button onclick="window.print()" class="btn-action btn-print">
                <i class="fas fa-print"></i>
                طباعة
            </button>
            
            <a href="#" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            
            <a href="{% url 'fuel_storage:payments_list' %}" class="btn-action btn-back">
                <i class="fas fa-arrow-right"></i>
                العودة
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات تفاعلية للعناصر
        const detailItems = document.querySelectorAll('.detail-item');
        detailItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });
        });
        
        // تحديث عنوان الصفحة
        document.title = `تفاصيل الدفعة - {{ payment.payment_number }}`;
    });
</script>
{% endblock %}
