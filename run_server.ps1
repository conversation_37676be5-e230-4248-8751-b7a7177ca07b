# PowerShell script to run the Fuel Storage Management System
# سكريبت PowerShell لتشغيل نظام إدارة مخازن المحروقات

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   نظام إدارة مخازن المحروقات" -ForegroundColor Yellow
Write-Host "   Fuel Storage Management System" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "تفعيل البيئة الافتراضية..." -ForegroundColor Green
Write-Host "Activating virtual environment..." -ForegroundColor Green

# Activate virtual environment
& "venv\Scripts\Activate.ps1"

Write-Host ""
Write-Host "فحص النظام..." -ForegroundColor Green
Write-Host "Checking system..." -ForegroundColor Green

# Check system
$checkResult = & python manage.py check
if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "خطأ في فحص النظام!" -ForegroundColor Red
    Write-Host "System check failed!" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
    exit 1
}

Write-Host ""
Write-Host "تطبيق التحديثات على قاعدة البيانات..." -ForegroundColor Green
Write-Host "Applying database migrations..." -ForegroundColor Green

# Apply migrations
& python manage.py migrate

Write-Host ""
Write-Host "إنشاء الملفات الثابتة..." -ForegroundColor Green
Write-Host "Collecting static files..." -ForegroundColor Green

# Collect static files
& python manage.py collectstatic --noinput

Write-Host ""
Write-Host "تشغيل الخادم..." -ForegroundColor Green
Write-Host "Starting server..." -ForegroundColor Green
Write-Host ""

Write-Host "يمكنك الوصول للنظام عبر:" -ForegroundColor Yellow
Write-Host "You can access the system at:" -ForegroundColor Yellow
Write-Host ""
Write-Host "النظام الرئيسي: " -NoNewline -ForegroundColor White
Write-Host "http://localhost:8000/" -ForegroundColor Cyan
Write-Host "Main System: " -NoNewline -ForegroundColor White
Write-Host "http://localhost:8000/" -ForegroundColor Cyan
Write-Host ""
Write-Host "لوحة التحكم الإدارية: " -NoNewline -ForegroundColor White
Write-Host "http://localhost:8000/admin/" -ForegroundColor Cyan
Write-Host "Admin Dashboard: " -NoNewline -ForegroundColor White
Write-Host "http://localhost:8000/admin/" -ForegroundColor Cyan
Write-Host ""
Write-Host "بيانات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "Login credentials:" -ForegroundColor Yellow
Write-Host "اسم المستخدم / Username: " -NoNewline -ForegroundColor White
Write-Host "Abdulelah" -ForegroundColor Green
Write-Host "البريد الإلكتروني / Email: " -NoNewline -ForegroundColor White
Write-Host "<EMAIL>" -ForegroundColor Green
Write-Host ""
Write-Host "اضغط Ctrl+C لإيقاف الخادم" -ForegroundColor Red
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Red
Write-Host ""

# Start server
try {
    & python manage.py runserver 8000
}
catch {
    Write-Host "حدث خطأ أثناء تشغيل الخادم" -ForegroundColor Red
    Write-Host "An error occurred while starting the server" -ForegroundColor Red
}

Write-Host ""
Write-Host "تم إيقاف الخادم" -ForegroundColor Yellow
Write-Host "Server stopped" -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج / Press Enter to exit"
