{% extends 'base.html' %}

{% block title %}عمليات الصادر - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">عمليات الصادر</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/outgoingoperation/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة عملية صادر جديدة
        </a>
    </div>
</div>

{% if operations %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>الرقم الورقي</th>
                    <th>المخزن</th>
                    <th>المستفيد</th>
                    <th>تاريخ العملية</th>
                    <th>المسلم</th>
                    <th>المستلم</th>
                    <th>أنشئ بواسطة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for operation in operations %}
                <tr>
                    <td>{{ operation.paper_number }}</td>
                    <td>{{ operation.storage.name }}</td>
                    <td>{{ operation.beneficiary.full_name }}</td>
                    <td>{{ operation.operation_date|date:"Y-m-d H:i" }}</td>
                    <td>{{ operation.deliverer_name }}</td>
                    <td>{{ operation.receiver_name }}</td>
                    <td>{{ operation.created_by.full_name|default:operation.created_by.username }}</td>
                    <td>{{ operation.created_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <a href="/admin/fuel_storage/outgoingoperation/{{ operation.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد عمليات صادر مسجلة حتى الآن.
    </div>
{% endif %}
{% endblock %}
