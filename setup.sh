#!/bin/bash

# إنشاء البيئة الافتراضية
echo "إنشاء البيئة الافتراضية..."
python -m venv venv

# تفعيل البيئة الافتراضية
echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate  # على Linux/macOS
# venv\Scripts\activate  # على Windows

# تثبيت المكتبات المطلوبة
echo "تثبيت المكتبات المطلوبة..."
pip install -r requirements.txt

# إنشاء المجلدات المطلوبة
echo "إنشاء المجلدات المطلوبة..."
mkdir -p media/incoming_operations
mkdir -p media/outgoing_operations
mkdir -p media/incoming_returns
mkdir -p media/outgoing_returns
mkdir -p media/damage_operations
mkdir -p media/storage_transfers
mkdir -p static
mkdir -p logs

# إنشاء الهجرات
echo "إنشاء الهجرات..."
python manage.py makemigrations fuel_storage
python manage.py migrate

# إنشاء مستخدم إداري
echo "إنشاء مستخدم إداري..."
echo "من فضلك أدخل بيانات المستخدم الإداري:"
python manage.py createsuperuser

# تشغيل الخادم
echo "تشغيل الخادم..."
python manage.py runserver
