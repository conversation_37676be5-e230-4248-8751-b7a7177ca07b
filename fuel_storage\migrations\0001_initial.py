# Generated by Django 4.2.7 on 2025-08-04 02:31

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('user_type', models.CharField(choices=[('manager', 'مدير'), ('operator', 'مشغل'), ('accountant', 'محاسب'), ('viewer', 'مستعرض')], default='operator', max_length=20, verbose_name='نوع المستخدم')),
                ('phone_number', models.CharField(blank=True, max_length=17, null=True, verbose_name='رقم الهاتف')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='القسم')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Beneficiary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المستفيد')),
                ('beneficiary_type', models.CharField(choices=[('government', 'حكومي'), ('private', 'خاص'), ('individual', 'فردي')], default='government', max_length=20, verbose_name='نوع المستفيد')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان')),
                ('discount_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الخصم %')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مستفيد',
                'verbose_name_plural': 'المستفيدون',
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الصنف')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الصنف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('minimum_stock_level', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock_level', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
            },
        ),
        migrations.CreateModel(
            name='DamageOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('damage_date', models.DateTimeField(verbose_name='تاريخ التلف')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('estimated_loss', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الخسارة المقدرة')),
                ('insurance_claim', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مطالبة التأمين')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('damage_cause', models.TextField(verbose_name='سبب التلف')),
                ('investigation_report', models.TextField(blank=True, null=True, verbose_name='تقرير التحقيق')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_damage_operations', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية تلف',
                'verbose_name_plural': 'عمليات التلف',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ عملية الوارد')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('supply_document_number', models.CharField(max_length=50, verbose_name='رقم سند التوريد')),
                ('invoice_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الفاتورة')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('net_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الصافي')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_incoming_operations', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية وارد',
                'verbose_name_plural': 'عمليات الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_date', models.DateTimeField(verbose_name='تاريخ المرتجع')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('refund_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الاسترداد')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('return_reason', models.TextField(verbose_name='سبب الإرجاع')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد المراد الإرجاع منها')),
            ],
            options={
                'verbose_name': 'مرتجع وارد',
                'verbose_name_plural': 'مرتجعات الوارد',
            },
        ),
        migrations.CreateModel(
            name='InventoryAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_date', models.DateTimeField(verbose_name='تاريخ التسوية')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('adjustment_type', models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص'), ('correction', 'تصحيح')], max_length=20, verbose_name='نوع التسوية')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة الإجمالية')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ عملية الصادر')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('invoice_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الفاتورة')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الضريبة')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الخصم')),
                ('net_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الصافي')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_outgoing_operations', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('beneficiary', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.beneficiary', verbose_name='المستفيد')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية صادر',
                'verbose_name_plural': 'عمليات الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_date', models.DateTimeField(verbose_name='تاريخ المرتجع')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='مبلغ الائتمان')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('return_reason', models.TextField(verbose_name='سبب الإرجاع')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر المراد الإرجاع منها')),
            ],
            options={
                'verbose_name': 'مرتجع صادر',
                'verbose_name_plural': 'مرتجعات الصادر',
            },
        ),
        migrations.CreateModel(
            name='Station',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المحطة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المحطة')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('region', models.CharField(max_length=50, verbose_name='المنطقة')),
                ('manager_name', models.CharField(max_length=100, verbose_name='اسم المدير')),
                ('phone_number', models.CharField(max_length=17, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'محطة',
                'verbose_name_plural': 'المحطات',
            },
        ),
        migrations.CreateModel(
            name='Storage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المخزن')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المخزن')),
                ('classification', models.CharField(choices=[('main', 'مخزن رئيسي'), ('secondary', 'مخزن فرعي'), ('temporary', 'مخزن مؤقت'), ('emergency', 'مخزن طوارئ')], max_length=20, verbose_name='التصنيف')),
                ('location', models.TextField(verbose_name='الموقع')),
                ('capacity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='السعة الإجمالية')),
                ('keeper_name', models.CharField(max_length=100, verbose_name='أمين المخزن')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='هاتف المخزن')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='مكيف الحرارة')),
                ('security_level', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي')], default='medium', max_length=20, verbose_name='مستوى الأمان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
            },
        ),
        migrations.CreateModel(
            name='StorageTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateTimeField(verbose_name='تاريخ النقل')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('transfer_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة النقل')),
                ('insurance_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة التأمين')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة الإجمالية')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('transfer_method', models.CharField(choices=[('truck', 'شاحنة'), ('pipeline', 'أنبوب'), ('rail', 'سكة حديد'), ('ship', 'سفينة'), ('other', 'أخرى')], default='truck', max_length=50, verbose_name='طريقة النقل')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('normal', 'عادي'), ('high', 'عالي'), ('urgent', 'عاجل')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from', to='fuel_storage.storage', verbose_name='المخزن المحول منه')),
                ('to_storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_to', to='fuel_storage.storage', verbose_name='المخزن المحول إليه')),
            ],
            options={
                'verbose_name': 'عملية تحويل مخزني',
                'verbose_name_plural': 'عمليات التحويل المخزني',
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المورد')),
                ('supplier_type', models.CharField(choices=[('local', 'محلي'), ('international', 'دولي'), ('government', 'حكومي')], default='local', max_length=20, verbose_name='نوع المورد')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+*********'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان')),
                ('payment_terms', models.IntegerField(default=30, verbose_name='شروط الدفع (أيام)')),
                ('rating', models.IntegerField(default=3, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='التقييم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردون',
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('approve', 'اعتماد'), ('reject', 'رفض'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('export', 'تصدير'), ('import', 'استيراد')], max_length=20, verbose_name='الإجراء')),
                ('model_name', models.CharField(max_length=50, verbose_name='اسم النموذج')),
                ('object_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='معرف الكائن')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل النظام',
                'verbose_name_plural': 'سجلات النظام',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='StorageTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transferred_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المحولة')),
                ('unit_cost', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة الإجمالية')),
                ('reason', models.CharField(choices=[('rebalancing', 'إعادة توزيع'), ('maintenance', 'صيانة المخزن'), ('capacity', 'سعة المخزن'), ('location', 'قرب الموقع'), ('emergency', 'حالة طوارئ'), ('optimization', 'تحسين التشغيل'), ('demand', 'طلب السوق'), ('cost_reduction', 'تقليل التكلفة'), ('quality_control', 'مراقبة الجودة'), ('other', 'أخرى')], max_length=50, verbose_name='السبب')),
                ('condition_before', models.CharField(choices=[('excellent', 'ممتاز'), ('good', 'جيد'), ('fair', 'مقبول'), ('poor', 'ضعيف')], default='good', max_length=20, verbose_name='الحالة قبل النقل')),
                ('condition_after', models.CharField(blank=True, choices=[('excellent', 'ممتاز'), ('good', 'جيد'), ('fair', 'مقبول'), ('poor', 'ضعيف'), ('damaged', 'تالف')], max_length=20, null=True, verbose_name='الحالة بعد النقل')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage_transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.storagetransfer', verbose_name='عملية التحويل')),
            ],
            options={
                'verbose_name': 'صنف محول',
                'verbose_name_plural': 'أصناف التحويل',
            },
        ),
        migrations.CreateModel(
            name='StorageTransferFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='storage_transfers/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_type', models.CharField(choices=[('transfer_order', 'أمر نقل'), ('receipt', 'إيصال'), ('inspection', 'فحص'), ('insurance', 'تأمين'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('storage_transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.storagetransfer', verbose_name='عملية التحويل')),
            ],
            options={
                'verbose_name': 'ملف مرفق للتحويل',
                'verbose_name_plural': 'الملفات المرفقة للتحويل',
            },
        ),
        migrations.CreateModel(
            name='StorageItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_of_measure', models.CharField(choices=[('liter', 'لتر'), ('gallon', 'جالون'), ('barrel', 'برميل'), ('ton', 'طن'), ('kg', 'كيلوجرام'), ('cubic_meter', 'متر مكعب')], max_length=20, verbose_name='وحدة القياس')),
                ('opening_balance', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية الحالية')),
                ('reserved_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المحجوزة')),
                ('reorder_point', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='نقطة إعادة الطلب')),
                ('last_purchase_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='آخر سعر شراء')),
                ('average_cost', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='متوسط التكلفة')),
                ('location_in_storage', models.CharField(blank=True, max_length=50, null=True, verbose_name='الموقع في المخزن')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'صنف مخزن',
                'verbose_name_plural': 'أصناف المخازن',
                'unique_together': {('storage', 'category')},
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('out_of_stock', 'نفاد المخزون'), ('expiry_warning', 'تحذير انتهاء الصلاحية'), ('expired', 'منتهي الصلاحية'), ('overstock', 'مخزون زائد')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('medium', 'متوسط'), ('high', 'عالي'), ('critical', 'حرج')], max_length=10, verbose_name='الأولوية')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='محلول')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='حُل بواسطة')),
                ('storage_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storageitem', verbose_name='صنف المخزن')),
            ],
            options={
                'verbose_name': 'تنبيه مخزون',
                'verbose_name_plural': 'تنبيهات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QualityControl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inspection_date', models.DateTimeField(verbose_name='تاريخ الفحص')),
                ('inspection_type', models.CharField(choices=[('incoming', 'فحص الوارد'), ('periodic', 'فحص دوري'), ('random', 'فحص عشوائي'), ('complaint', 'فحص شكوى')], max_length=20, verbose_name='نوع الفحص')),
                ('inspector_name', models.CharField(max_length=100, verbose_name='اسم المفتش')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('sample_size', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='حجم العينة')),
                ('density', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='الكثافة')),
                ('viscosity', models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True, verbose_name='اللزوجة')),
                ('flash_point', models.DecimalField(blank=True, decimal_places=1, max_digits=6, null=True, verbose_name='نقطة الوميض')),
                ('water_content', models.DecimalField(blank=True, decimal_places=3, max_digits=5, null=True, verbose_name='محتوى الماء %')),
                ('sulfur_content', models.DecimalField(blank=True, decimal_places=3, max_digits=5, null=True, verbose_name='محتوى الكبريت %')),
                ('quality_status', models.CharField(choices=[('passed', 'مقبول'), ('failed', 'مرفوض'), ('conditional', 'مشروط'), ('pending', 'قيد المراجعة')], max_length=20, verbose_name='حالة الجودة')),
                ('overall_grade', models.CharField(choices=[('A+', 'ممتاز'), ('A', 'ممتاز'), ('B+', 'جيد'), ('B', 'جيد'), ('C', 'مقبول'), ('D', 'ضعيف'), ('F', 'راسب')], max_length=5, verbose_name='التقدير العام')),
                ('recommendations', models.TextField(blank=True, null=True, verbose_name='التوصيات')),
                ('corrective_actions', models.TextField(blank=True, null=True, verbose_name='الإجراءات التصحيحية')),
                ('next_inspection_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الفحص القادم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('storage_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storageitem', verbose_name='صنف المخزن')),
            ],
            options={
                'verbose_name': 'مراقبة جودة',
                'verbose_name_plural': 'مراقبة الجودة',
                'ordering': ['-inspection_date'],
            },
        ),
        migrations.CreateModel(
            name='PriceList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم قائمة الأسعار')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئت بواسطة')),
            ],
            options={
                'verbose_name': 'قائمة أسعار',
                'verbose_name_plural': 'قوائم الأسعار',
                'ordering': ['-effective_date'],
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('returned_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('unit_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='سعر الوحدة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('expected_return_date', models.DateField(verbose_name='تاريخ الرد المفترض')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')),
                ('return_reason', models.CharField(choices=[('unused', 'غير مستخدم'), ('defective', 'معيب'), ('wrong_specification', 'مواصفات خاطئة'), ('excess_quantity', 'كمية زائدة'), ('other', 'أخرى')], max_length=100, verbose_name='سبب الإرجاع')),
                ('condition', models.CharField(choices=[('new', 'جديد'), ('good', 'جيد'), ('damaged', 'تالف')], default='good', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('outgoing_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
            ],
            options={
                'verbose_name': 'صنف مرتجع صادر',
                'verbose_name_plural': 'أصناف مرتجع الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturnFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='outgoing_returns/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('outgoing_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
            ],
            options={
                'verbose_name': 'ملف مرفق لمرتجع الصادر',
                'verbose_name_plural': 'الملفات المرفقة لمرتجع الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('exported_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المصروفة')),
                ('unit_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='السعر الإجمالي')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('actual_transfer_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التحويل الفعلي')),
                ('cost_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='سعر التكلفة')),
                ('profit_margin', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='هامش الربح')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
            ],
            options={
                'verbose_name': 'صنف صادر',
                'verbose_name_plural': 'أصناف الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='outgoing_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_type', models.CharField(choices=[('invoice', 'فاتورة'), ('delivery_note', 'مذكرة تسليم'), ('receipt', 'إيصال'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
            ],
            options={
                'verbose_name': 'ملف مرفق للصادر',
                'verbose_name_plural': 'الملفات المرفقة للصادر',
            },
        ),
        migrations.AddField(
            model_name='outgoingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='OperationModification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('incoming', 'وارد'), ('outgoing', 'صادر')], max_length=20, verbose_name='نوع العملية')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ العملية')),
                ('previous_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية السابقة')),
                ('new_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الجديدة')),
                ('previous_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='السعر السابق')),
                ('new_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='السعر الجديد')),
                ('reason', models.TextField(verbose_name='سبب التعديل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('modification_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعديل')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
                ('modified_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='عدل بواسطة')),
                ('outgoing_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تعديل عملية',
                'verbose_name_plural': 'تعديلات العمليات',
            },
        ),
        migrations.CreateModel(
            name='MaintenanceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('preventive', 'وقائية'), ('corrective', 'تصحيحية'), ('emergency', 'طارئة'), ('routine', 'دورية')], max_length=20, verbose_name='نوع الصيانة')),
                ('scheduled_date', models.DateTimeField(verbose_name='تاريخ الصيانة المجدولة')),
                ('actual_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الصيانة الفعلي')),
                ('duration_hours', models.DecimalField(decimal_places=2, max_digits=5, verbose_name='المدة بالساعات')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('technician_name', models.CharField(max_length=100, verbose_name='اسم الفني')),
                ('cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة')),
                ('status', models.CharField(choices=[('scheduled', 'مجدولة'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغية'), ('postponed', 'مؤجلة')], default='scheduled', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'جدولة صيانة',
                'verbose_name_plural': 'جدولة الصيانة',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='InventoryAdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('book_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الدفترية')),
                ('physical_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الفعلية')),
                ('difference', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الفرق')),
                ('unit_cost', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='القيمة الإجمالية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.inventoryadjustment', verbose_name='التسوية')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'صنف تسوية',
                'verbose_name_plural': 'أصناف التسوية',
            },
        ),
        migrations.AddField(
            model_name='inventoryadjustment',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='IncomingReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('returned_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('unit_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='سعر الوحدة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الإجمالي')),
                ('expected_return_date', models.DateField(verbose_name='تاريخ الرد المفترض')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')),
                ('return_reason', models.CharField(choices=[('defective', 'معيب'), ('expired', 'منتهي الصلاحية'), ('wrong_specification', 'مواصفات خاطئة'), ('excess_quantity', 'كمية زائدة'), ('other', 'أخرى')], max_length=100, verbose_name='سبب الإرجاع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
            ],
            options={
                'verbose_name': 'صنف مرتجع وارد',
                'verbose_name_plural': 'أصناف مرتجع الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingReturnFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='incoming_returns/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('incoming_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
            ],
            options={
                'verbose_name': 'ملف مرفق لمرتجع الوارد',
                'verbose_name_plural': 'الملفات المرفقة لمرتجع الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('imported_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المستوردة')),
                ('unit_price', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='السعر الإجمالي')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم %')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('quality_grade', models.CharField(choices=[('A', 'ممتاز'), ('B', 'جيد'), ('C', 'مقبول')], default='A', max_length=20, verbose_name='درجة الجودة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
            ],
            options={
                'verbose_name': 'صنف وارد',
                'verbose_name_plural': 'أصناف الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='incoming_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_type', models.CharField(choices=[('invoice', 'فاتورة'), ('receipt', 'إيصال'), ('certificate', 'شهادة'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
            ],
            options={
                'verbose_name': 'ملف مرفق',
                'verbose_name_plural': 'الملفات المرفقة',
            },
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='station',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.station', verbose_name='المحطة'),
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.supplier', verbose_name='المورد'),
        ),
        migrations.CreateModel(
            name='DamageOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('damaged_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية التالفة')),
                ('unit_cost', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_loss', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي الخسارة')),
                ('reason', models.CharField(choices=[('expired', 'منتهي الصلاحية'), ('contaminated', 'ملوث'), ('leaked', 'تسرب'), ('fire', 'حريق'), ('accident', 'حادث'), ('quality_issue', 'مشكلة في الجودة'), ('theft', 'سرقة'), ('natural_disaster', 'كارثة طبيعية'), ('equipment_failure', 'عطل في المعدات'), ('human_error', 'خطأ بشري'), ('other', 'أخرى')], max_length=50, verbose_name='السبب')),
                ('severity', models.CharField(choices=[('minor', 'طفيف'), ('moderate', 'متوسط'), ('major', 'كبير'), ('total', 'كلي')], default='moderate', max_length=20, verbose_name='درجة الخطورة')),
                ('salvage_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='قيمة الإنقاذ')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('damage_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.damageoperation', verbose_name='عملية التلف')),
            ],
            options={
                'verbose_name': 'صنف تالف',
                'verbose_name_plural': 'أصناف التلف',
            },
        ),
        migrations.CreateModel(
            name='DamageOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='damage_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_type', models.CharField(choices=[('photo', 'صورة'), ('report', 'تقرير'), ('insurance', 'تأمين'), ('investigation', 'تحقيق'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('damage_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.damageoperation', verbose_name='عملية التلف')),
            ],
            options={
                'verbose_name': 'ملف مرفق للتلف',
                'verbose_name_plural': 'الملفات المرفقة للتلف',
            },
        ),
        migrations.AddField(
            model_name='damageoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='CategoryPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_price', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='سعر الشراء')),
                ('selling_price', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='سعر البيع')),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='سعر الجملة')),
                ('currency', models.CharField(default='SAR', max_length=3, verbose_name='العملة')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15.0, max_digits=5, verbose_name='معدل الضريبة %')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('price_list', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.pricelist', verbose_name='قائمة الأسعار')),
            ],
            options={
                'verbose_name': 'سعر صنف',
                'verbose_name_plural': 'أسعار الأصناف',
                'unique_together': {('category', 'price_list')},
            },
        ),
    ]
