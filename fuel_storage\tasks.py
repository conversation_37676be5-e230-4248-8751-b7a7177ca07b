from celery import shared_task
from django.core.management import call_command
from django.core.mail import send_mail
from django.conf import settings
from .models import StockAlert, MaintenanceSchedule, StorageItem
from django.utils import timezone
from datetime import timedelta

@shared_task
def generate_daily_alerts():
    """مهمة يومية لإنشاء التنبيهات"""
    call_command('generate_alerts')
    return "تم إنشاء التنبيهات اليومية"

@shared_task
def cleanup_old_logs():
    """مهمة أسبوعية لتنظيف السجلات القديمة"""
    call_command('cleanup_logs', '--days=90')
    return "تم تنظيف السجلات القديمة"

@shared_task
def send_critical_alerts_email():
    """إرسال تنبيهات حرجة بالبريد الإلكتروني"""
    critical_alerts = StockAlert.objects.filter(
        priority='critical',
        is_read=False,
        created_at__gte=timezone.now() - timedelta(hours=24)
    )
    
    if critical_alerts.exists():
        message = "التنبيهات الحرجة:\n\n"
        for alert in critical_alerts:
            message += f"- {alert.message}\n"
        
        send_mail(
            'تنبيهات حرجة - نظام إدارة مخازن المحروقات',
            message,
            settings.DEFAULT_FROM_EMAIL,
            ['<EMAIL>'],  # قائمة المستلمين
            fail_silently=False,
        )
        
        return f"تم إرسال {critical_alerts.count()} تنبيه حرج"
    
    return "لا توجد تنبيهات حرجة"

@shared_task
def update_average_costs():
    """تحديث متوسط التكلفة شهرياً"""
    call_command('update_average_costs')
    return "تم تحديث متوسط التكلفة"

@shared_task
def generate_monthly_report():
    """إنشاء تقرير شهري"""
    # يمكن إضافة منطق إنشاء التقرير الشهري هنا
    return "تم إنشاء التقرير الشهري"
