{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الحسابات{% endblock %}

{% block extra_css %}
<style>
    .account-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        border-left: 5px solid #667eea;
    }
    
    .account-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .account-type-asset {
        border-left-color: #27ae60;
    }
    
    .account-type-liability {
        border-left-color: #e74c3c;
    }
    
    .account-type-equity {
        border-left-color: #3498db;
    }
    
    .account-type-revenue {
        border-left-color: #f39c12;
    }
    
    .account-type-expense {
        border-left-color: #9b59b6;
    }
    
    .account-type-cost_of_goods {
        border-left-color: #e67e22;
    }
    
    .account-balance {
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .balance-positive {
        color: #27ae60;
    }
    
    .balance-negative {
        color: #e74c3c;
    }
    
    .balance-zero {
        color: #95a5a6;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .account-code {
        background: #f8f9fa;
        padding: 5px 10px;
        border-radius: 20px;
        font-family: monospace;
        font-weight: bold;
    }
    
    .account-type-badge {
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .type-asset {
        background: #d5f4e6;
        color: #27ae60;
    }
    
    .type-liability {
        background: #fadbd8;
        color: #e74c3c;
    }
    
    .type-equity {
        background: #d6eaf8;
        color: #3498db;
    }
    
    .type-revenue {
        background: #fdeaa7;
        color: #f39c12;
    }
    
    .type-expense {
        background: #e8daef;
        color: #9b59b6;
    }
    
    .type-cost_of_goods {
        background: #f0b27a;
        color: #e67e22;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-list me-2"></i>
                        قائمة الحسابات
                    </h1>
                    <p class="text-muted">إدارة ومراقبة جميع الحسابات المحاسبية</p>
                </div>
                <div>
                    <a href="{% url 'fuel_storage:financial_dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="account_type" class="form-label">نوع الحساب</label>
                <select name="account_type" id="account_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in account_types %}
                        <option value="{{ value }}" {% if current_filter == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="البحث في اسم الحساب، الرمز، أو الوصف..." 
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
                <a href="{% url 'fuel_storage:accounts_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>

    <!-- Accounts Grid -->
    <div class="row">
        {% for account in accounts %}
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="account-card account-type-{{ account.account_type.account_type }}">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="mb-1">{{ account.name }}</h5>
                        <span class="account-code">{{ account.code }}</span>
                    </div>
                    <span class="account-type-badge type-{{ account.account_type.account_type }}">
                        {{ account.account_type.get_account_type_display }}
                    </span>
                </div>
                
                <div class="mb-3">
                    {% if account.description %}
                        <p class="text-muted small mb-2">{{ account.description|truncatewords:10 }}</p>
                    {% endif %}
                    <p class="text-muted small mb-0">
                        <i class="fas fa-folder me-1"></i>
                        {{ account.account_type.name }}
                    </p>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">الرصيد الحالي</small>
                        <div class="account-balance 
                            {% if account.current_balance > 0 %}balance-positive
                            {% elif account.current_balance < 0 %}balance-negative
                            {% else %}balance-zero{% endif %}">
                            {{ account.current_balance|floatformat:2 }}
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <small class="text-muted d-block">الرصيد الافتتاحي</small>
                        <small class="fw-bold">{{ account.opening_balance|floatformat:2 }}</small>
                    </div>
                </div>
                
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ account.created_at|date:"Y/m/d" }}
                        </small>
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            {{ account.created_by.full_name }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد حسابات</h4>
                <p class="text-muted">لم يتم العثور على حسابات تطابق معايير البحث</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Summary Section -->
    {% if accounts %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="filter-section">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    ملخص الحسابات
                </h5>
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-coins fa-2x text-success"></i>
                        </div>
                        <h6>الأصول</h6>
                        <p class="text-success fw-bold">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-credit-card fa-2x text-danger"></i>
                        </div>
                        <h6>الخصوم</h6>
                        <p class="text-danger fw-bold">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-chart-pie fa-2x text-primary"></i>
                        </div>
                        <h6>حقوق الملكية</h6>
                        <p class="text-primary fw-bold">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-arrow-up fa-2x text-warning"></i>
                        </div>
                        <h6>الإيرادات</h6>
                        <p class="text-warning fw-bold">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-arrow-down fa-2x" style="color: #9b59b6;"></i>
                        </div>
                        <h6>المصروفات</h6>
                        <p class="fw-bold" style="color: #9b59b6;">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-2">
                            <i class="fas fa-box fa-2x" style="color: #e67e22;"></i>
                        </div>
                        <h6>تكلفة البضاعة</h6>
                        <p class="fw-bold" style="color: #e67e22;">
                            {{ accounts|length }} حساب
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير hover للبطاقات
        const cards = document.querySelectorAll('.account-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
        
        // تحديث العداد
        const accountCount = {{ accounts|length }};
        document.title = `قائمة الحسابات (${accountCount})`;
    });
</script>
{% endblock %}
