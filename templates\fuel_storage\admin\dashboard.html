{% extends 'fuel_storage/admin/base.html' %}

{% block title %}لوحة التحكم الإدارية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt text-primary"></i>
        لوحة التحكم الإدارية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ total_users }}</div>
                    <div>إجمالي المستخدمين</div>
                    <small class="opacity-75">نشط: {{ active_users }}</small>
                </div>
                <div class="ms-3">
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ total_categories }}</div>
                    <div>إجمالي الأصناف</div>
                    <small class="opacity-75">نشط: {{ active_categories }}</small>
                </div>
                <div class="ms-3">
                    <i class="fas fa-tags fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ total_storages }}</div>
                    <div>إجمالي المخازن</div>
                    <small class="opacity-75">نشط: {{ active_storages }}</small>
                </div>
                <div class="ms-3">
                    <i class="fas fa-warehouse fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="stats-number">{{ total_suppliers }}</div>
                    <div>إجمالي الموردين</div>
                    <small class="opacity-75">نشط: {{ active_suppliers }}</small>
                </div>
                <div class="ms-3">
                    <i class="fas fa-truck fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'fuel_storage:admin:user_create' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>إضافة مستخدم جديد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'fuel_storage:admin:category_create' %}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>إضافة صنف جديد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'fuel_storage:admin:storage_create' %}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-warehouse fa-2x mb-2"></i>
                            <span>إضافة مخزن جديد</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'fuel_storage:admin:supplier_create' %}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center" style="min-height: 120px;">
                            <i class="fas fa-truck fa-2x mb-2"></i>
                            <span>إضافة مورد جديد</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قوائم الإدارة -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i>
                    إدارة البيانات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{% url 'fuel_storage:admin:user_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-users text-primary me-2"></i>
                            إدارة المستخدمين
                        </div>
                        <span class="badge bg-primary rounded-pill">{{ total_users }}</span>
                    </a>
                    <a href="{% url 'fuel_storage:admin:category_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-tags text-success me-2"></i>
                            إدارة الأصناف
                        </div>
                        <span class="badge bg-success rounded-pill">{{ total_categories }}</span>
                    </a>
                    <a href="{% url 'fuel_storage:admin:station_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-gas-pump text-info me-2"></i>
                            إدارة المحطات
                        </div>
                        <span class="badge bg-info rounded-pill">{{ total_stations }}</span>
                    </a>
                    <a href="{% url 'fuel_storage:admin:supplier_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-truck text-warning me-2"></i>
                            إدارة الموردين
                        </div>
                        <span class="badge bg-warning rounded-pill">{{ total_suppliers }}</span>
                    </a>
                    <a href="{% url 'fuel_storage:admin:beneficiary_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-users-cog text-secondary me-2"></i>
                            إدارة المستفيدين
                        </div>
                        <span class="badge bg-secondary rounded-pill">{{ total_beneficiaries }}</span>
                    </a>
                    <a href="{% url 'fuel_storage:admin:storage_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-warehouse text-dark me-2"></i>
                            إدارة المخازن
                        </div>
                        <span class="badge bg-dark rounded-pill">{{ total_storages }}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exchange-alt"></i>
                    إدارة العمليات
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{% url 'fuel_storage:admin:incoming_operation_list' %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-arrow-down text-success me-2"></i>
                                عمليات الوارد
                            </h6>
                        </div>
                        <p class="mb-1">إدارة عمليات استلام البضائع من الموردين</p>
                    </a>
                    <a href="{% url 'fuel_storage:admin:outgoing_operation_list' %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-arrow-up text-danger me-2"></i>
                                عمليات الصادر
                            </h6>
                        </div>
                        <p class="mb-1">إدارة عمليات تسليم البضائع للمستفيدين</p>
                    </a>
                    <a href="{% url 'fuel_storage:admin:damage_operation_list' %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                عمليات التلف
                            </h6>
                        </div>
                        <p class="mb-1">إدارة عمليات تسجيل التلف والفقدان</p>
                    </a>
                    <a href="{% url 'fuel_storage:admin:storage_transfer_list' %}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-exchange-alt text-info me-2"></i>
                                النقل المخزني
                            </h6>
                        </div>
                        <p class="mb-1">إدارة عمليات النقل بين المخازن</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>المستخدم الحالي:</strong> {{ user.full_name }} ({{ user.get_user_type_display }})</p>
                        <p><strong>آخر تسجيل دخول:</strong> {{ user.last_login|date:"Y-m-d H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>إصدار النظام:</strong> 2.0</p>
                        <p><strong>تاريخ آخر تحديث:</strong> {{ "now"|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
