# دليل البدء السريع | Quick Start Guide

## 🚀 تشغيل النظام | Running the System

### الطريقة الأولى: ملف Batch (الأسهل)
```bash
# انقر مرتين على الملف أو شغله من Command Prompt
run_server.bat
```

### الطريقة الثانية: PowerShell
```powershell
# شغل من PowerShell
.\run_server.ps1
```

### الطريقة الثالثة: يدوياً
```bash
# تفعيل البيئة الافتراضية
venv\Scripts\activate

# تشغيل الخادم
python manage.py runserver 8000
```

## 🌐 الوصول للنظام | System Access

### لوحة التحكم الإدارية الجديدة
- **الرابط**: http://localhost:8000/admin/
- **الوصف**: النظام الإداري الشامل المخصص

### النظام الرئيسي
- **الرابط**: http://localhost:8000/
- **الوصف**: النظام الأساسي لإدارة العمليات

## 🔐 بيانات تسجيل الدخول | Login Credentials

- **اسم المستخدم**: `Abdulelah`
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: التي أدخلتها عند إنشاء المستخدم

## ✅ الميزات المتاحة | Available Features

### إدارة البيانات الأساسية:
- ✅ **المستخدمين**: إضافة، تعديل، حذف، تفعيل/إلغاء تفعيل
- ✅ **الأصناف**: إدارة أصناف المحروقات والمواد
- ✅ **المحطات**: إدارة محطات الوقود
- ✅ **الموردين**: إدارة الموردين وحدود الائتمان
- ✅ **المستفيدين**: إدارة المستفيدين من الخدمات
- ✅ **المخازن**: إدارة المخازن والسعات

### الميزات المتقدمة:
- 🔍 **البحث والفلترة**: في جميع الجداول
- 📄 **التصفح**: للجداول الكبيرة
- ⚡ **عمليات سريعة**: تفعيل/إلغاء تفعيل بدون إعادة تحميل
- 🛡️ **نظام صلاحيات**: محكم ومرن
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشكلة Django غير مثبت:
```bash
# تفعيل البيئة الافتراضية أولاً
venv\Scripts\activate

# تثبيت Django
pip install django
```

### مشكلة في البيئة الافتراضية:
```bash
# إعادة إنشاء البيئة الافتراضية
python -m venv venv --clear
venv\Scripts\activate
pip install django Pillow reportlab openpyxl python-decouple
```

### مشكلة في قاعدة البيانات:
```bash
# تطبيق التحديثات
python manage.py migrate
```

### إنشاء مستخدم مدير جديد:
```bash
python manage.py createsuperuser
```

## 📋 نصائح مهمة | Important Tips

1. **تأكد من تفعيل البيئة الافتراضية** قبل تشغيل أي أمر
2. **استخدم المتصفح الحديث** للحصول على أفضل تجربة
3. **احفظ بياناتك بانتظام** من خلال النظام
4. **راجع السجلات** في مجلد `logs/` عند حدوث مشاكل

## 🆘 الحصول على المساعدة | Getting Help

- راجع ملف `ADMIN_SYSTEM_GUIDE.md` للدليل الشامل
- راجع ملف `TROUBLESHOOTING.md` لحل المشاكل الشائعة
- تحقق من سجلات النظام في مجلد `logs/`

## 🎯 الخطوات التالية | Next Steps

1. **سجل دخولك** للنظام الإداري
2. **استكشف الواجهات** المختلفة
3. **أضف بيانات تجريبية** لاختبار النظام
4. **تعرف على الميزات** المتاحة

---

**ملاحظة**: النظام الآن جاهز للاستخدام مع واجهات إدارية مخصصة وحديثة! 🎉
