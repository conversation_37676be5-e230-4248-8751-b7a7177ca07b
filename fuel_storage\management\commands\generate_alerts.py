from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from fuel_storage.models import StorageItem, StockAlert, MaintenanceSchedule

class Command(BaseCommand):
    help = 'إنشاء تنبيهات المخزون والصيانة'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء التنبيهات...')
        
        # تنبيهات المخزون
        storage_items = StorageItem.objects.all()
        alerts_created = 0
        
        for item in storage_items:
            # تنبيه المخزون المنخفض
            if item.is_low_stock() and item.current_quantity > 0:
                alert, created = StockAlert.objects.get_or_create(
                    storage_item=item,
                    alert_type='low_stock',
                    is_resolved=False,
                    defaults={
                        'priority': 'medium',
                        'message': f'مخزون منخفض لـ {item.category.name} في {item.storage.name}'
                    }
                )
                if created:
                    alerts_created += 1
            
            # تنبيه نفاد المخزون
            if item.current_quantity <= 0:
                alert, created = StockAlert.objects.get_or_create(
                    storage_item=item,
                    alert_type='out_of_stock',
                    is_resolved=False,
                    defaults={
                        'priority': 'high',
                        'message': f'نفد مخزون {item.category.name} في {item.storage.name}'
                    }
                )
                if created:
                    alerts_created += 1
            
            # تنبيه انتهاء الصلاحية
            if item.is_expired():
                alert, created = StockAlert.objects.get_or_create(
                    storage_item=item,
                    alert_type='expired',
                    is_resolved=False,
                    defaults={
                        'priority': 'critical',
                        'message': f'انتهت صلاحية {item.category.name} في {item.storage.name}'
                    }
                )
                if created:
                    alerts_created += 1
            
            # تحذير قرب انتهاء الصلاحية
            elif item.expiry_date and item.days_until_expiry() and item.days_until_expiry() <= 30:
                alert, created = StockAlert.objects.get_or_create(
                    storage_item=item,
                    alert_type='expiry_warning',
                    is_resolved=False,
                    defaults={
                        'priority': 'medium',
                        'message': f'سينتهي صلاحية {item.category.name} في {item.storage.name} خلال {item.days_until_expiry()} يوم'
                    }
                )
                if created:
                    alerts_created += 1
        
        # تنبيهات الصيانة المتأخرة
        overdue_maintenance = MaintenanceSchedule.objects.filter(
            status='scheduled',
            scheduled_date__lt=timezone.now()
        )
        
        for maintenance in overdue_maintenance:
            storage_item = maintenance.storage.storageitem_set.first()
            if storage_item:
                alert, created = StockAlert.objects.get_or_create(
                    storage_item=storage_item,
                    alert_type='low_stock',  # استخدام نوع موجود
                    is_resolved=False,
                    defaults={
                        'priority': 'high',
                        'message': f'صيانة متأخرة للمخزن {maintenance.storage.name}'
                    }
                )
                if created:
                    alerts_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {alerts_created} تنبيه جديد')
        )
