{% extends 'base.html' %}

{% block title %}ملخص المرتجعات - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">ملخص المرتجعات</h1>
</div>

<!-- إحصائيات المرتجعات -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي مرتجعات الوارد
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_incoming_returns }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-undo fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            إجمالي مرتجعات الصادر
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_outgoing_returns }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-redo fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث المرتجعات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">أحدث مرتجعات الوارد</h6>
            </div>
            <div class="card-body">
                {% if recent_incoming_returns %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>العملية الأصلية</th>
                                    <th>تاريخ المرتجع</th>
                                    <th>أنشئ بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for return in recent_incoming_returns %}
                                <tr>
                                    <td>{{ return.paper_number }}</td>
                                    <td>{{ return.incoming_operation.paper_number }}</td>
                                    <td>{{ return.return_date|date:"Y-m-d" }}</td>
                                    <td>{{ return.created_by.full_name|default:return.created_by.username }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد مرتجعات وارد حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">أحدث مرتجعات الصادر</h6>
            </div>
            <div class="card-body">
                {% if recent_outgoing_returns %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>العملية الأصلية</th>
                                    <th>تاريخ المرتجع</th>
                                    <th>أنشئ بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for return in recent_outgoing_returns %}
                                <tr>
                                    <td>{{ return.paper_number }}</td>
                                    <td>{{ return.outgoing_operation.paper_number }}</td>
                                    <td>{{ return.return_date|date:"Y-m-d" }}</td>
                                    <td>{{ return.created_by.full_name|default:return.created_by.username }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد مرتجعات صادر حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="/admin/fuel_storage/incomingreturn/add/" class="btn btn-outline-info btn-block">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مرتجع وارد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/admin/fuel_storage/outgoingreturn/add/" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مرتجع صادر
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'fuel_storage:incoming_returns_list' %}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-list me-1"></i>
                            عرض مرتجعات الوارد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'fuel_storage:outgoing_returns_list' %}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-list me-1"></i>
                            عرض مرتجعات الصادر
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
