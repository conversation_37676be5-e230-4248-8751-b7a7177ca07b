{% extends 'base.html' %}

{% block title %}تقرير حركة عامة للمخزن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">تقرير حركة عامة للمخزن</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{% url 'fuel_storage:reports_dashboard' %}" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للتقارير
        </a>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">فلاتر البحث</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="storage_id" class="form-label">المخزن</label>
                <select name="storage_id" id="storage_id" class="form-select" required>
                    <option value="">اختر المخزن</option>
                    {% for storage in storages %}
                        <option value="{{ storage.id }}" {% if selected_storage and selected_storage.id == storage.id %}selected{% endif %}>
                            {{ storage.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="from_date" class="form-label">من تاريخ</label>
                <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date }}">
            </div>
            <div class="col-md-3">
                <label for="to_date" class="form-label">إلى تاريخ</label>
                <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

{% if selected_storage %}
    <!-- معلومات المخزن -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">معلومات المخزن: {{ selected_storage.name }}</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>التصنيف:</strong> {{ selected_storage.get_classification_display }}
                </div>
                <div class="col-md-3">
                    <strong>أمين المخزن:</strong> {{ selected_storage.keeper_name }}
                </div>
                <div class="col-md-3">
                    <strong>الهاتف:</strong> {{ selected_storage.phone_number }}
                </div>
                <div class="col-md-3">
                    <div class="btn-group" role="group">
                        <a href="?storage_id={{ selected_storage.id }}&from_date={{ from_date }}&to_date={{ to_date }}&export=pdf" 
                           class="btn btn-sm btn-outline-danger" data-export="pdf">
                            <i class="fas fa-file-pdf me-1"></i>PDF
                        </a>
                        <a href="?storage_id={{ selected_storage.id }}&from_date={{ from_date }}&to_date={{ to_date }}&export=excel" 
                           class="btn btn-sm btn-outline-success" data-export="excel">
                            <i class="fas fa-file-excel me-1"></i>Excel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- عمليات الوارد -->
    {% if incoming_operations %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0 text-success">
                <i class="fas fa-arrow-down me-2"></i>عمليات الوارد ({{ incoming_operations.count }})
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>الرقم الورقي</th>
                            <th>المورد</th>
                            <th>المحطة</th>
                            <th>التاريخ</th>
                            <th>الأصناف</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for operation in incoming_operations %}
                        <tr>
                            <td>{{ operation.paper_number }}</td>
                            <td>{{ operation.supplier.full_name }}</td>
                            <td>{{ operation.station.name }}</td>
                            <td class="date-display">{{ operation.operation_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% for item in operation.items.all %}
                                    <span class="badge bg-info me-1">{{ item.category.name }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- عمليات الصادر -->
    {% if outgoing_operations %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0 text-danger">
                <i class="fas fa-arrow-up me-2"></i>عمليات الصادر ({{ outgoing_operations.count }})
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-sm">
                    <thead>
                        <tr>
                            <th>الرقم الورقي</th>
                            <th>المستفيد</th>
                            <th>التاريخ</th>
                            <th>الأصناف</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for operation in outgoing_operations %}
                        <tr>
                            <td>{{ operation.paper_number }}</td>
                            <td>{{ operation.beneficiary.full_name }}</td>
                            <td class="date-display">{{ operation.operation_date|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% for item in operation.items.all %}
                                    <span class="badge bg-warning me-1">{{ item.category.name }}</span>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if not incoming_operations and not outgoing_operations %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد عمليات في الفترة المحددة لهذا المخزن.
    </div>
    {% endif %}

{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        يرجى اختيار مخزن لعرض التقرير الخاص به.
    </div>
{% endif %}
{% endblock %}
